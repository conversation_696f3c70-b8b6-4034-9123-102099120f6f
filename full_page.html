<html lang="en-us" slick-uniqueid="3"><head>
  <title>
    Ace Hardware at Bizaglo - Bizaglo LLC - Optizmo Client Management
  </title>

  
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
  
  <meta name="description" content="">
  <meta name="keywords" content="">
  <meta name="verify-v1" content="ZxqhcdegLIEoNSXoHrwG3WicpDQAcwanPnEoFYc8gOM=">
  <meta name="robots" content="noindex">
  <link rel="stylesheet" type="text/css" href="/tmpl_main.css?v=1748490996">
  
    <link rel="stylesheet" type="text/css" href="/tmpl_hybrid.css?v=1748490996">
  
  <link rel="stylesheet" type="text/css" href="/tmpl_main_print.css?v=1748490996" media="print">
  <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32-production.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16-production.png">

  <!-- Optizmo Insight required asset -->
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600&amp;display=swap" rel="stylesheet">

  <!-- Google Tag Manager -->
  <script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-0MGEMX2439&amp;cx=c&amp;gtm=45He55r0v812090216za200&amp;tag_exp=101509157~103116026~103130498~103130500~103200004~103233427~103252644~103252646~104481633~104481635"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-PL64M6M"></script><script>(function (w, d, s, l, i) {
      w[l] = w[l] || []; w[l].push({
        'gtm.start':
          new Date().getTime(), event: 'gtm.js'
      }); var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-PL64M6M');</script>
  <!-- End Google Tag Manager -->
  <script defer="" type="text/javascript" src="/include/alpinejs-3.10.5.min.js"></script>
  <script type="text/javascript" src="/include/mootools-core-1.6.0.js"></script>
  <script type="text/javascript" src="/include/mootools-more-1.6.0.js"></script>
  <script type="text/javascript" src="/include/mootools-manual-compat-1.6.0.js"></script>
  <script type="text/javascript" src="/include/moocheck.js"></script>
  <script type="text/javascript" src="/include/formcheck-1.4/formcheck.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/validator/13.0.0/validator.min.js" integrity="sha256-gyO4new2/pN9zvXDLCWLyO7siPKq5pSJ7wL8UzDMmpI=" crossorigin="anonymous"></script>
  <link rel="stylesheet" type="text/css" media="screen" href="/include/formcheck-1.4/theme/classic/formcheck.css">
  <!-- LOAD CORE.JS LAST FOR DOMREADY ORDERING (IE SUCKS) -->

  <script type="text/javascript" src="/include/overlay-boxes.js"></script>
  <script type="text/javascript" src="/include/TableMagic.js?v=6"></script>

  
    <style type="text/css">
      body {
        display: none;
      }
    </style>
  

  <script type="text/javascript">
    window.addEvent('domready', function () {
      FancyForm.start($$('input.checkbox'));

      if ($('message_icon')) {
        $('message_icon').addEvent('click', function (e) {
          $('messages').setStyle('right', 0);

          // Removed overlay box as it was always returning true.
          new Request.JSON({
            url: "messages.html&a=Read&id=145",
            async: true,
            noCache: true,
            onSuccess: function (rJson, rText) {
              // Success
              if (rJson && rJson.result == 1) {
                $('message_icon').removeClass('active');
                $('message_icon').getChildren('i').set('html', '');

                // Failed
              } else {
                alert('failed');
              }
            }
          }).get();
        });
      }

      $$('#messages .entry').each(function (el, i) {
        el.addEvent('click', function (e) {
          window.location = el.get('data-link');
        });
      });

      $(document.body).addEvent('click', function (e) {
        if (e.target.get('id') != 'message_icon' && e.target.getParent().get('id') != 'message_icon' && e.target && !$(e.target).getParents().contains($('messages'))) {
          $('messages').setStyle('right', '-300px');
        }
      });

      $('close_messages').addEvent('click', function (e) {
        $('messages').setStyle('right', '-300px');
      });
    });
  </script>

  <script type="text/javascript">
    window.API_URL = 'https://api.optizmo.com';
    window.API_AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0eXBlIjoic3VwcHJlc3MtdXNlciIsInVzZXJfaWQiOjMzMzAsImNsaWVudF9pZCI6MjI3ODMsImlhdCI6MTc0ODUxNDk4OSwiZXhwIjoxNzUwMzI5Mzg5fQ.Xkv6CpCpRBo0IxhiHTN3wcC8Mtgd5hA4o5YTUUsuMaU';
    window.ENVIRONMENT = 'production';
    // Configure React app asset path at runtime to support multiple environments from the same bundle.
    window.__assetsPath = (path) => `https://suppress.optizmo.com/${path}`;
  </script>

  

  
    <!-- Start of optizmo Zendesk Widget script -->
    <script>
      window.zESettings = {
        webWidget: {
          helpCenter: {
            filter: {
              // Limit widget HC results to "Classic SUPPRESS" and "ACCESS" articles.
              category: '202668857-SUPPRESS,360002546433-ACCESS'
            }
          }
        }
      };
    </script>
    <iframe src="javascript:false" title="" role="presentation" style="display: none;"></iframe><script>/*<![CDATA[*/window.zEmbed || function (e, t) { var n, o, d, i, s, a = [], r = document.createElement("iframe"); window.zEmbed = function () { a.push(arguments) }, window.zE = window.zE || window.zEmbed, r.src = "javascript:false", r.title = "", r.role = "presentation", (r.frameElement || r).style.cssText = "display: none", d = document.getElementsByTagName("script"), d = d[d.length - 1], d.parentNode.insertBefore(r, d), i = r.contentWindow, s = i.document; try { o = s } catch (e) { n = document.domain, r.src = 'javascript:var d=document.open();d.domain="' + n + '";void(0);', o = s } o.open()._l = function () { var o = this.createElement("script"); n && (this.domain = n), o.id = "js-iframe-async", o.src = e, this.t = +new Date, this.zendeskHost = t, this.zEQueue = a, this.body.appendChild(o) }, o.write('<body onload="document._l();">'), o.close() }("https://assets.zendesk.com/embeddable_framework/main.js", "optizmo.zendesk.com");
  /*]]>*/</script>
    <!-- End of optizmo Zendesk Widget script -->

    <script>
      zE(function () {
        zE.identify({
          name: 'Conner Finnegan (Bizaglo LLC)',
          email: '<EMAIL>'
        });
      });
    </script>
  

  <script>
    // Used for GTM user session tracking.
    window.CLIENT_USER_ID = window.CLIENT_USER_ID || '3330';
  </script>

<style data-emotion="optizmo-global" data-s=""></style><style data-emotion="optizmo" data-s=""></style></head>

<body style="padding: 0px; margin: 0px; background: rgb(240, 240, 237); height: 100%; display: block;">

  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PL64M6M" height="0" width="0"
      style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->

  <div id="messages">
    <h2>Messages</h2>

    <div id="close_messages">X</div>
    <div class="viewall">
      <a href="messages.html">View All Messages</a>
    </div>

    
      
        
        <div data-link="messages.html&amp;id=145" class="entry important ">
    <h4>
      Update to our Terms of Service
    </h4>
    <h4 class="attribs"><span>
        Dec 01, 2022
      </span>
      Important Update
    </h4>
    <p>
      Recent update to OPTIZMO Terms of Service <a href="messages.html&amp;id=145">Read More</a>
    </p>
  </div>
  
        
          <div class="read-title">Read Messages</div>
        
        <div data-link="messages.html&amp;id=144" class="entry important 
          read-messages
    ">
    <h4>
      Updates to our Terms of Service
    </h4>
    <h4 class="attribs"><span>
        Jun 15, 2021
      </span>
      Important Update
    </h4>
    <p>
      We made a few recent changes to our Terms of Service <a href="messages.html&amp;id=144">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=142" class="entry important 
          read-messages
    ">
    <h4>
      Mailer Access Key Enhancement
    </h4>
    <h4 class="attribs"><span>
        Jul 22, 2020
      </span>
      Important Update
    </h4>
    <p>
      We're making an update to the domain used in our Mailer Access Keys. <a href="messages.html&amp;id=142">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=143" class="entry important 
          read-messages
    ">
    <h4>
      Updates to our Terms of Service
    </h4>
    <h4 class="attribs"><span>
        Jun 26, 2020
      </span>
      Important Update
    </h4>
    <p>
      We have made several updates to our Terms of Service. <a href="messages.html&amp;id=143">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=141" class="entry news 
          read-messages
    ">
    <h4>
      Short OPTIZMO Survey
    </h4>
    <h4 class="attribs"><span>
        Apr 28, 2020
      </span>
      News Update
    </h4>
    <p>
      A short survey about the rollout of ACCESS <a href="messages.html&amp;id=141">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=139" class="entry feature 
          read-messages
    ">
    <h4>
      Global Opt-Out List Feature Release
    </h4>
    <h4 class="attribs"><span>
        Apr 07, 2020
      </span>
      Feature Update
    </h4>
    <p>
      A new ability to append your Global Opt-Out list to a regular campaign list <a href="messages.html&amp;id=139">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=138" class="entry feature 
          read-messages
    ">
    <h4>
      Ensuring The Security of your Suppression Data
    </h4>
    <h4 class="attribs"><span>
        Mar 18, 2020
      </span>
      Feature Update
    </h4>
    <p>
      Support for SHA-512 Hashing <a href="messages.html&amp;id=138">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=137" class="entry news 
          read-messages
    ">
    <h4>
      ACCESS Rollout Update
    </h4>
    <h4 class="attribs"><span>
        Feb 12, 2020
      </span>
      News Update
    </h4>
    <p>
      Update on ACCESS Rollout progress <a href="messages.html&amp;id=137">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=136" class="entry important 
          read-messages
    ">
    <h4>
      ACCESS Phased Rollout Begins Today
    </h4>
    <h4 class="attribs"><span>
        Jan 15, 2020
      </span>
      Important Update
    </h4>
    <p>
      Initial phased rollout of ACCESS starts today. <a href="messages.html&amp;id=136">Read More</a>
    </p>
  </div>
  
        
        <div data-link="messages.html&amp;id=135" class="entry important 
          read-messages
    ">
    <h4>
      Announcing ACCESS
    </h4>
    <h4 class="attribs"><span>
        Dec 17, 2019
      </span>
      Important Update
    </h4>
    <p>
      Upcoming enhancement to our process for mailer access to suppression lists and opt-out links <a href="messages.html&amp;id=135">Read More</a>
    </p>
  </div>
  
  
    </div>

    
      <!-- Header and navigation are provided by React. -->
      <div id="suppress-react-navigation"><div class="MuiScopedCssBaseline-root optizmo-qlnvfq"><div class="optizmo-16dxmuo-root"><header class="MuiPaper-root MuiPaper-elevation MuiPaper-elevation6 MuiAppBar-root MuiAppBar-colorDefault MuiAppBar-positionFixed mui-fixed optizmo-or6uee" style="--Paper-shadow: 0px 8px 16px 0px rgba(82,63,105,0.10);"><div class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular optizmo-1b608w7"><div class="optizmo-wg7z36-title"><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone optizmo-u5rv55-logoLink" data-testid="logo-link" href="/"><svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" id="Layer_1" x="0" y="0" viewBox="0 0 3000 775.53" class="optizmo-1w1wena-logo"><title>Optizmo</title><style>.st0{fill:#fff}</style><path d="M1646.07 198.76v.14h-73.27v245.85h-76.34V198.9h-79.55c-9.59-30.34-28.16-56.72-52.56-76.03h281.52c-5.86 11.31-9.18 24.15-9.18 37.75 0 13.75 3.37 26.73 9.38 38.14zm1243.38-34.11c-31.19-31.38-70.92-49.23-119.17-49.23-49.14 0-89.16 17.7-120.05 48.77-30.9 31.08-46.34 72.56-46.34 121.56 0 49.01 15.89 89.63 47.67 121.91 31.78 32.27 71.5 48.4 119.17 48.4 47.96 0 87.54-15.98 118.73-47.95 31.19-31.97 46.79-72.45 46.79-121.46-.01-48.99-15.61-90.62-46.8-122zm-54.23 189.31c-17.03 17.93-38.6 26.17-64.72 26.17-26.12 0-47.7-8.97-64.72-26.89-17.03-17.93-25.54-37.9-25.54-65.69 0-27.78 8.66-50.94 25.98-69.47 17.32-18.52 39.04-27.78 65.16-27.78 25.83 0 47.18 9.19 64.06 27.56 16.88 18.38 25.32 41.61 25.32 69.69 0 27.79-8.52 48.48-25.54 66.41zM889.27 115.43c-49.14 0-89.16 17.7-120.05 48.77-30.9 31.08-46.34 72.56-46.34 121.56 0 49.01 15.89 89.63 47.67 121.91 31.78 32.27 71.5 48.4 119.17 48.4 47.96 0 87.54-15.98 118.73-47.95 31.19-31.97 46.79-72.45 46.79-121.46 0-49-15.6-90.63-46.79-122-31.2-31.38-70.93-49.23-119.18-49.23zm64.94 238.53c-17.03 17.93-38.6 26.17-64.72 26.17-26.12 0-47.7-8.97-64.72-26.89-17.03-17.93-25.54-37.9-25.54-65.69 0-27.78 8.66-50.94 25.98-69.47 17.32-18.52 39.04-27.78 65.16-27.78 25.83 0 47.18 9.19 64.06 27.56 16.88 18.38 25.32 41.61 25.32 69.69 0 27.79-8.52 48.48-25.54 66.41zm805.42-121.36v212.16h-78.55V232.6a83.173 83.173 0 0 0 39.25 9.8 83.34 83.34 0 0 0 39.3-9.8zm-39.29-123.85c-27.63 0-50.02 22.4-50.02 50.02s22.4 50.02 50.02 50.02c27.63 0 50.02-22.4 50.02-50.02s-22.39-50.02-50.02-50.02zm-453.43 14.12h-108.67v.01h-69.51v321.89h78v-65.79l.02.01h100.17c68.12 0 123.37-55.2 123.37-123.32v-9.42c-.01-68.13-55.26-123.38-123.38-123.38zm31.32 161.47c-9.22 9.22-21.96 14.89-35.98 14.89h-95.53V197.48h95.53c28.1 0 50.88 22.77 50.88 50.88 0 14.07-5.72 26.76-14.9 35.98zm1138.51-172.19c-26.47 0-50.44 6.05-67.74 18.69-8.84 6.44-15.92 14.63-20.78 24.6-5.25-6.79-12.6-13.92-22.97-21.97-16.92-13.14-47.72-21.32-74.19-21.32-72.54 0-131.4 58.8-131.4 131.4v201.2h79.7V244.13c0-29.98 24.5-54.48 54.48-54.48 14.99 0 28.59 6.1 38.43 16 9.9 9.9 16 23.49 16 38.48v200.63h77.01V244.13c0-14.99 6.1-28.59 16-38.48 9.85-9.9 20.56-16 35.55-16 29.98 0 51.6 24.5 51.6 54.48v200.63h79.7v-201.2c.01-72.6-58.85-131.41-131.39-131.41zm-532.02 254.49h183.82v78.12h-234.41c-52.41 0-80.76-61.45-44.05-95.56l161.52-150.04h-177.52v-.96c6.29-11.77 9.9-25.17 9.9-39.44 0-14.27-3.6-27.67-9.9-39.44h240.16c10.38 0 20.61 3.12 29.11 9.13 13.98 9.85 20.56 25.85 23.93 42.09 3.07 14.65 1.15 34.93-11.19 47.18-2.98 3.03-2.64 2.88-5.67 5.53-13.79 11.91-27.53 23.83-41.27 35.74-18.45 15.9-36.85 31.85-55.25 47.75-16.14 13.98-32.24 27.96-48.38 41.89-6.91 6-13.83 12-20.8 18.01zM802.76 544.78h-20.44v-10.67h52.71v10.67h-20.35v120.21h-11.92V544.78zm166.31-10.67h41.15v11.03h-29.14v47.16h28.78v10.67h-28.78v51.01h30.03v11.03h-42.04v-130.9zm228.07 127.39c-2.51 1.44-5.21 2.52-8.11 3.27-2.9.75-5.87 1.12-8.92 1.12-11.83 0-20.6-5.36-26.31-16.09-5.71-10.73-8.56-27.15-8.56-49.26 0-22.06 3.05-38.76 9.14-50.12 6.1-11.35 15.09-17.03 26.98-17.03 2.99 0 5.81.3 8.47.9 2.66.6 5.21 1.52 7.67 2.78v12.82c-2.81-1.85-5.4-3.2-7.76-4.03-2.36-.84-4.71-1.26-7.04-1.26-8.6 0-14.92 4.4-18.96 13.18-4.03 8.79-6.05 22.5-6.05 41.15 0 20.26 1.87 34.59 5.6 42.98 3.73 8.4 9.93 12.59 18.6 12.59 1.79 0 3.51-.18 5.16-.53 1.64-.36 3.24-.9 4.79-1.61.9-.42 1.79-.91 2.69-1.48s1.76-1.18 2.6-1.84v12.46zm134.04-127.39h12.01v52.71h31.2v-52.71h11.92v130.88h-11.92v-67.77h-31.2v67.77h-12.01V534.11zm200.64 0h10.31l29.58 89.55c.42 1.26 1.26 4.32 2.51 9.19s2.81 10.98 4.66 18.33c-.96-7.83-1.66-15.83-2.11-24.02-.45-8.19-.67-16.65-.67-25.37v-67.68h10.31v130.88h-10.31l-28.87-88.12c-1.32-4.18-2.63-8.62-3.95-13.31-1.31-4.69-2.69-10-4.12-15.91 1.02 9.27 1.76 17.99 2.24 26.18.48 8.19.72 15.81.72 22.86V665h-10.31V534.11zm193.56 65.53c0-22.95 2.63-39.89 7.89-50.83 5.26-10.94 13.45-16.41 24.56-16.41 11.12 0 19.32 5.47 24.61 16.41 5.29 10.94 7.93 27.88 7.93 50.83s-2.64 39.85-7.93 50.69c-5.29 10.85-13.49 16.27-24.61 16.27-11.11 0-19.3-5.42-24.56-16.27-5.26-10.84-7.89-27.74-7.89-50.69zm32.45-56.21c-6.93 0-12.03 4.5-15.28 13.49-3.26 8.99-4.89 23.17-4.89 42.53 0 19.37 1.63 33.57 4.89 42.63 3.26 9.05 8.35 13.58 15.28 13.58 6.99 0 12.12-4.47 15.37-13.4 3.26-8.93 4.89-23.2 4.89-42.81 0-19.54-1.63-33.77-4.89-42.67-3.26-8.9-8.38-13.35-15.37-13.35zm171.69-9.32h12.01v120.04h28.06V665h-40.07V534.11zm165.5 65.53c0-22.95 2.63-39.89 7.89-50.83 5.26-10.94 13.45-16.41 24.56-16.41 11.12 0 19.32 5.47 24.61 16.41 5.29 10.94 7.93 27.88 7.93 50.83s-2.64 39.85-7.93 50.69c-5.29 10.85-13.49 16.27-24.61 16.27-11.11 0-19.3-5.42-24.56-16.27s-7.89-27.74-7.89-50.69zm32.45-56.21c-6.93 0-12.03 4.5-15.28 13.49-3.26 8.99-4.89 23.17-4.89 42.53 0 19.37 1.63 33.57 4.89 42.63 3.26 9.05 8.35 13.58 15.28 13.58 6.99 0 12.12-4.47 15.37-13.4 3.26-8.93 4.89-23.2 4.89-42.81 0-19.54-1.63-33.77-4.89-42.67-3.26-8.9-8.38-13.35-15.37-13.35zm223.32 13.45-11.03 4.57c-1.37-5.8-3.38-10.19-6.01-13.18s-5.83-4.49-9.59-4.49c-6.46 0-11.25 4.57-14.39 13.72-3.14 9.14-4.71 23.13-4.71 41.95 0 20.02 1.48 34.39 4.44 43.12 2.96 8.73 7.78 13.09 14.48 13.09 5.14 0 8.94-2.28 11.39-6.86 2.45-4.57 3.67-11.73 3.67-21.47v-19.81h-18.2V596.5h29.94v29.4c0 13.62-2.29 23.85-6.86 30.66-4.57 6.81-11.4 10.22-20.48 10.22-10.64 0-18.41-5.33-23.31-16-4.9-10.66-7.35-27.71-7.35-51.14 0-22.89 2.62-39.82 7.84-50.79 5.23-10.96 13.31-16.45 24.25-16.45 6.1 0 11.37 2.11 15.82 6.32 4.47 4.22 7.83 10.27 10.1 18.16zm137.62 108.11V534.11h12.01v130.88h-12.01zm157.35-130.88h41.15v11.03h-29.14v47.16h28.78v10.67h-28.78v51.01h30.03v11.03h-42.04v-130.9zm177.42 110.71c3.17 3.53 6.48 6.18 9.95 7.98 3.47 1.8 7.08 2.69 10.85 2.69 6.1 0 11.1-2.21 15.02-6.63s5.87-10.13 5.87-17.12c0-2.99-.4-5.74-1.21-8.25-.81-2.51-2.05-4.84-3.72-6.99-2.57-3.29-7.11-7.32-13.63-12.1-2.63-1.91-4.57-3.37-5.83-4.39-6.63-5.38-11.42-10.68-14.34-15.91-2.93-5.23-4.39-11.07-4.39-17.52 0-9.8 2.9-17.95 8.7-24.43 5.8-6.48 13.06-9.73 21.78-9.73 3.46 0 6.89.6 10.27 1.79 3.38 1.2 6.68 2.99 9.91 5.38v15.42c-2.63-3.71-5.62-6.53-8.97-8.47-3.35-1.94-6.9-2.91-10.67-2.91-5.68 0-10.28 2.02-13.81 6.05-3.53 4.03-5.29 9.31-5.29 15.82 0 5.5 1.19 10.23 3.58 14.21s8.04 9.28 16.94 15.91c9.2 6.75 15.27 12.63 18.2 17.61 2.93 4.99 4.39 10.92 4.39 17.8 0 10.4-2.99 18.93-8.96 25.59-5.98 6.67-13.57 10-22.77 10-4 0-7.8-.51-11.38-1.52-3.59-1.01-7.08-2.57-10.49-4.66v-15.62z" class="st0" style="display: block;"></path><path d="M588 687.76H125.69c-34.06 0-61.92-27.87-61.92-61.92V149.69c0-34.06 27.87-61.92 61.92-61.92H588c34.06 0 61.92 27.87 61.92 61.92v476.15c.01 34.06-27.86 61.92-61.92 61.92z" style="fill: rgb(64, 160, 214);"></path><path d="M388.42 658.95c-19.82.61-39.71-1.39-58.89-4.66-19.23-3.21-37.64-8.1-55.33-13.04-1.7-.44-2.9-.35-2.33.56.58.92 2.36 1.97 4.32 2.55 1.06.3 2.13.6 3.21.9 1.08.27 2.17.54 3.26.82 2.17.56 4.35 1.13 6.55 1.7 2.17.62 4.42 1.08 6.64 1.63 2.22.55 4.45 1.1 6.69 1.65 2.26.49 4.52.99 6.8 1.48 2.28.48 4.52 1.08 6.85 1.48 4.63.84 9.21 1.9 13.92 2.58 18.71 3.14 38.11 5.12 57.28 4.67 2.31-.14 4.17-.69 4.29-1.37.31-.58-1.36-1.01-3.26-.95zm199.07-299.27c-.57-1.53-1.28-1.55-1.56-.32-.4.97-.23 3.19.34 4.47.58 1.59 1.16 3.19 1.74 4.8.29.81.57 1.62.86 2.44.25.82.5 1.65.75 2.48.97 3.32 1.97 6.7 2.86 10.15.79 3.45 1.56 6.95 2.32 10.5.7 3.55 1.21 7.13 1.81 10.76 2.11 14.48 2.9 29.41 2.88 44.01a261.285 261.285 0 0 1-8.49 31.34 278.77 278.77 0 0 1-5.64 15.18c-1.04 2.55-2.11 5.09-3.22 7.61-.55 1.24-1.45 2.41-1.71 3.76-.4 2.09 1.41 2.85 2.55.93 1.2-2.02 1.79-4.33 2.69-6.48 1.32-3 2.67-5.99 3.95-8.99 1.16-3.05 2.32-6.09 3.48-9.11 4.29-12.2 7.4-24.56 8.22-36.46 1.15-16.4.13-31.89-2.37-46.37-2.53-14.5-6.47-28.02-11.46-40.7z" class="st0"></path><path d="M614.16 342.26c-15.38-50.1-44.29-90.96-81.13-119.78-9.22-7.22-18.99-13.63-29.09-19.41-2.55-1.39-5.11-2.78-7.68-4.17-2.61-1.31-5.26-2.56-7.87-3.83-2.81-1.44-5.82-2.57-8.71-3.87-1.16-.49-2.32-.98-3.49-1.48-1.32-.5-2.64-1-3.97-1.5-5.45-2.16-11.09-3.94-16.73-5.74-2.85-.79-5.71-1.59-8.59-2.4-2.88-.77-5.82-1.37-8.74-2.09-11.74-2.59-23.83-4.4-36.31-5.37-12.88-.96-25.59-.99-38.08-.03a273.1 273.1 0 0 0-36.73 5.37c-24 5.1-46.7 14.3-67.48 26.73-2.56 1.62-5.19 3.13-7.7 4.83l-7.5 5.14-7.26 5.5c-2.42 1.84-4.69 3.87-7.04 5.79a229.915 229.915 0 0 0-25.84 26c-7.98 9.37-15.11 19.54-21.45 30.29a226.496 226.496 0 0 0-16.38 33.99c-11.23 29.61-15.8 61.81-13.14 93.35.86 10.23 3.48 20.15 4.59 30.28 1.75 16.01 5.44 31.99 10.97 47.39.43 1.18-1.33 1.87-1.83.72-13.52-30.91-20.63-64.64-20.47-98.39.09-18.67 2.54-37.22 6.38-55.47l1.91-7.39c7.86-30.26 22.19-58.97 41.45-83.57 12.22-15.61 26.59-30.11 42.77-41.64 2.57-1.83 4.82-3.86 6.93-5.66 2.1-1.82 3.9-3.64 5.32-5.36 1.4-1.76 2.51-3.3 3.12-4.7.61-1.4.74-2.62.27-3.57-.47-.92-1.49-1.46-2.97-1.65-1.46-.17-3.42-.06-5.58.56-4.35 1.19-10 3.69-15.36 7.7-24.5 17.3-45.53 39.39-61.88 64.51-17.96 27.59-30.34 58.95-36.14 91.36-.69 3.87-1.34 7.77-1.85 11.67-2.87 22.03-3.05 44.45-.07 66.49.81 6.95 2.16 13.81 3.46 20.65l2.37 10.21c.8 3.4 1.85 6.74 2.78 10.1 1.2 4.08 2.51 8.13 3.91 12.15-1.17-.08-2.51.73-2.5 1.96.01.88.11 1.91.31 3.02.22 1.11.58 2.28 1.02 3.5 5.51 15.5 12.06 30.69 20.26 45 0 .01-.01.02-.01.03-.14 2.12-.56 2.11-.85 1.82-16.41-26.31-28.06-55.78-34.17-86.27-6.3-31.51-7.05-64.33-1.91-96.31.65-3.53.29-6.37-.7-6.53-1.06-.24-2.4 2.5-3.06 6.07-5.83 33.64-4.86 68.3 2.39 101.3 2.09 9.6 4.75 19.08 7.92 28.38-.3 1.04-1.05.6-1.17.52-.9-2.37-1.78-4.75-2.62-7.16-4.84-14.06-8.47-28.63-10.89-43.67-2.29-13.98-3.1-28.18-2.79-42.29.06-7.07.7-14.1 1.33-21.11.46-3.49.93-6.98 1.39-10.47.21-1.75.53-3.48.86-5.2l.97-5.18c2.74-13.78 6.42-27.35 11.34-40.45a283.32 283.32 0 0 1 17.52-37.98c6.78-12.18 14.58-23.78 23.08-34.82l3.23-4.12 1.62-2.06 1.7-1.99 6.83-7.94 7.17-7.66c2.39-2.55 4.97-4.93 7.45-7.4a304.043 304.043 0 0 1 32.45-27.05c11.56-8.23 23.77-15.68 36.57-22.33 2.22-1.03 3.34-2.22 3.21-2.81-.41-.58-2.52-.22-4.48.74-17.54 8.52-33.98 18.97-48.9 31.06-14.95 12.07-28.51 25.62-40.63 40.3a308.45 308.45 0 0 0-32.1 47.2c-9.23 16.73-17.07 34.35-23.48 52.75-6.9 19.87-11.25 40.46-12.39 61.43-1.2 20.95.58 42.24 5.66 63.25 6.89 28.2 17.86 53.24 31.23 76.27 6.78 11.49 14.06 22.57 22.06 33.34 8.06 10.73 16.7 21.23 26.14 31.64 1.21 1.4 2.8 2.45 3.58 2.33.47-.37-.06-1.66-1.28-3.07-2.15-2.42-4.27-4.85-6.35-7.3-.34-1.06-.26-1.68-.01-2.04 11.76 12.18 24.63 23.11 38.47 32.61a259.989 259.989 0 0 0 36.08 20.86 244.266 244.266 0 0 0 39.41 14.8c2.4.77 4.88 1.3 7.31 1.94 2.45.59 4.86 1.3 7.33 1.8 4.94 1.02 9.82 2.16 14.8 2.89 9.88 1.78 19.9 2.69 29.91 3.28 5.03.1 10.04.4 15.07.18 2.51-.07 5.03-.08 7.54-.21l7.54-.55c1.25-.11 2.51-.16 3.76-.31l3.75-.47c2.5-.32 5.02-.62 7.5-.98l7.3-1.31c2.43-.45 4.87-.84 7.27-1.45 19.31-4.26 38.13-10.91 55.82-19.89 17.61-9.1 34.06-20.51 48.74-33.91 11.6-10.59 21.56-22.69 30.26-35.52 3.96-5.84 7.92-11.68 11.55-17.73 1.91-3.19 3.92-6.34 5.66-9.63.97-1.83 1.78-3.78 2.5-5.72.28-.75.99-1.74.9-2.57-.04-.36-.15-.75-.4-.98-.15-.15-.35-.23-.62-.22-.48.02-.83.45-1.08.82-.76 1.13-1.28 2.52-1.91 3.73-1.67 3.04-3.25 6.14-4.99 9.14l-5.33 8.96-5.7 8.75c-1.91 2.92-4.02 5.69-6.02 8.55a290.242 290.242 0 0 1-27.24 32.08c-2.18 1.7-4.36 3.37-6.55 5.02-2.21 1.62-4.51 3.11-6.76 4.64l-3.39 2.25c-1.13.75-2.32 1.41-3.48 2.11-2.33 1.36-4.66 2.72-6.98 4.08-2.36 1.29-4.77 2.49-7.14 3.72l-3.57 1.82c-1.19.6-2.43 1.11-3.64 1.67l-7.28 3.26c-2.46 1.02-4.95 1.96-7.41 2.93l-3.7 1.45c-1.23.48-2.51.87-3.76 1.31-2.5.86-5.01 1.71-7.52 2.57-2.53.8-5.09 1.51-7.63 2.29l-3.81 1.15c-1.27.38-2.58.67-3.86 1.01-2.57.67-5.16 1.35-7.75 2.02-2.6.63-5.23 1.16-7.85 1.77-1.31.3-2.62.61-3.94.91-1.32.3-2.66.51-3.99.78-2.66.53-5.33 1.07-8.02 1.6-9.4 1.58-18.92 2.8-28.45 2.99-4.77.27-9.51.08-14.27-.01l-1.78-.04-1.77-.12-3.55-.26c-2.36-.2-4.74-.28-7.08-.6-4.69-.61-9.41-1.08-14.03-1.99-2.32-.42-4.65-.8-6.96-1.27l-6.88-1.57-3.43-.8-3.39-.94-6.78-1.9-6.67-2.19c-2.22-.73-4.45-1.44-6.62-2.31l-.74-.28c3.8 1.33 7.64 2.57 11.51 3.69 13.22 3.81 26.79 6.4 40.5 7.63 13.71 1.23 27.55 1.09 41.21-.52 1.71-.2 3.42-.38 5.12-.61l5.09-.87 5.07-.89c1.67-.35 3.31-.75 4.96-1.13 6.62-1.41 13.11-3.35 19.53-5.36 25.64-8.42 49.28-21.82 69.55-38.6 20.24-16.87 36.97-37.23 49.78-59.34 6.36-11.1 11.68-22.67 16.01-34.48.86-2.34 1.66-4.7 2.46-7.06 2.54-7.5 4.55-15.17 6.22-22.91.64-2.97 1.41-5.93 1.97-8.91.34-1.85.76-5.24-1.77-5.71-1.3-.25-2.27 1.14-2.8 2.12-.57 1.05-.88 2.25-1.15 3.39-.49 2-.96 4.01-1.61 5.97-.84 2.53-1.88 5.03-2.9 7.5-1.71 4.15-3.5 8.28-5.37 12.36-7.07 15.45-15.29 30.39-24.83 44.46-14.76 21.76-33.1 41.67-54.85 57.17-7.23 5.15-18.36 6.84-26.78 9.45-9.63 2.98-19.39 5.51-29.25 7.57 2.21-.46 4.56-1.66 6.69-2.47 15.3-5.82 31.78-14.47 44.63-24.94 22.68-17.06 39.17-38.85 50.16-61.9 3.81-8.01 7.04-16.3 9.66-24.78 2.53-8.16 5.25-16.49 6.67-24.91.7-4.18 1.27-8.37 1.7-12.58.2-1.94 2.59-11.45-1.63-11.56-2.28-.06-3.33 5.24-3.68 6.86-.49 2.3-1.04 4.59-1.63 6.87-1.17 4.56-2.47 9.09-4.02 13.54-13.68 39.23-40.26 74.33-74.73 97.6-36.81 24.84-83.28 34.66-126.93 26.29-3.05-.59-6.1-1.25-9.1-2.04-32.13-8.21-62.71-25.29-87.78-48.84-.4-.5-.8-.98-1.3-1.44-18.98-20.34-34.2-44.03-44-70.09-.83-2.32-2.01-4.3-3.09-5.69-1.06-1.39-2.18-2.13-3.08-1.87-.46.18-.83.57-1.08 1.16-3.28-8.09-5.87-16.44-8.06-24.88-4.75-18.32-6.68-35.75-3.86-54.54 1.02-6.8 2.35-13.47 3.98-20-.01.46-.18 9.38.44 19.53 0 .07.01.12.02.19-.58 28.83 5.05 57.51 16.27 82.87a188.04 188.04 0 0 0 23.24 39.26c9.14 11.89 19.72 22.31 31.03 31.23 22.59 17.92 48.77 29.08 73.95 33.27 25.25 4.11 49.39.99 68.18-7.56 1.26-.72 1.42-1.68.34-2.22-.42-.33-1.06-.43-1.77-.42-.72-.02-1.51.13-2.23.43-18.18 6.08-39.98 7.44-62.11 2.89-22.12-4.49-44.44-14.56-64.06-29.46-19.65-14.87-36.62-34.55-48.65-57.53-6.04-11.47-10.89-23.74-14.4-36.55-3.51-12.8-5.72-26.17-6.46-39.63 4.32-21.12 9.06-43.69 17.21-65.75 4.05-11.01 8.91-21.83 14.65-32.01 5.73-10.18 12.35-19.72 19.66-28.13 3.47-3.75 5.05-8.31 3.89-10.02-.61-.92-1.99-1.01-3.59-.33-1.59.69-3.45 2.11-5.18 4.01-5.88 6.24-11.48 12.9-16.32 20.14l-1.87 2.69c-.61.91-1.15 1.85-1.73 2.78l-3.44 5.6c-2.05 3.87-4.32 7.65-6.13 11.67 9.29-19.94 22.13-38.32 37.98-53.37 9.39-7.28 19.3-13.52 29.6-18.66.91-.44 1.79-.98 2.62-1.59.85-.57 1.65-1.18 2.35-1.84 1.4-1.31 2.41-2.76 2.7-4.02.09-.54-.02-.98-.24-1.34 11.25-6.35 23.11-11.25 35.2-14.86 12.12-3.61 24.4-5.8 37.29-6.91 12.72-1.09 25.36-.96 37.72.38 1.56.1 3.08.36 4.62.58l4.6.64c3.05.51 6.06 1.14 9.1 1.68 2.98.76 5.99 1.38 8.95 2.18l4.41 1.27 2.21.62c.74.21 1.45.49 2.17.73 23.24 7.56 44.7 19.74 63.34 36 18.64 16.27 34.51 36.48 47.39 59.41 1.6 3.46 3.57 6.11 5.39 7.67 1.75 1.58 3.44 2.01 4.62.83.59-.59 1-1.52 1.24-2.69.23-1.18.21-2.57.04-4.14-.17-1.57-.53-3.29-1.04-5.08-.27-.89-.54-1.81-.92-2.7-.38-.89-.81-1.78-1.27-2.67a182.005 182.005 0 0 0-24-39.85c-1.13-1.55-2.46-2.94-3.68-4.42l-3.74-4.34-3.96-4.12c-1.32-1.37-2.6-2.77-4.02-4.04-2.79-2.57-5.47-5.24-8.42-7.6l-4.32-3.63-4.51-3.39c-2.94-2.34-6.13-4.32-9.2-6.45-1.51-1.09-3.18-1.97-4.76-2.96-1.61-.95-3.17-1.96-4.8-2.88l-4.92-2.65-2.45-1.33-2.52-1.2-5.03-2.41c-1.68-.79-3.43-1.45-5.13-2.19-6.81-2.97-13.89-5.27-20.99-7.49-7.19-1.95-14.41-3.87-21.83-5.1-9.52-1.73-19.15-2.89-28.83-3.19-1.21-.04-2.41-.16-3.63-.15h-3.63c-2.42.02-4.84-.1-7.27.07l-7.27.29-7.26.62c-2.43.15-4.84.52-7.25.83-2.4.34-4.87.55-7.2 1.03l-7.09 1.25-7.01 1.61c-9.29 2.34-18.53 5.12-27.39 8.93-17.82 7.36-34.57 17.64-49.57 30.33-7.43 6.41-14.55 13.29-21.03 20.81-6.44 7.55-12.46 15.53-17.8 24.01a219.61 219.61 0 0 0-18.03 26c-2.26 3.82-4.34 7.75-6.3 11.73-.63 1.29-2.58.43-2.06-.91 3.43-8.78 7.72-17.33 12.02-25.34 7.2-13.41 15.77-26.1 25.62-37.71 15.51-18.29 33.71-33.77 53.75-45.97 20.13-12.09 42.06-20.8 65.13-26.12 23.29-5.15 47.44-7.46 72.28-6.06 11.73.61 23.26 2.3 34.55 4.79 11.29 2.5 22.34 5.87 33.08 10.08 21.33 8.62 41.47 20.47 59.3 35.49 35.46 29.6 62.41 70.61 77.88 117.53.83.7 1.61 1.74 2.79 1.77.88.02 2.62-.9 2.51-2.01-.11-1.88-.52-3.94-1.13-5.86zm-412.4 228.6a254.72 254.72 0 0 0 17.48 16.19 320.34 320.34 0 0 1-17.03-14.64c-3.84-4.27-7.51-8.66-11.05-13.15 3.42 3.98 6.95 7.85 10.6 11.6z" class="st0"></path><path d="M252.99 635.95c-28.01-12.16-54.29-30.47-76.56-53.77-22.34-23.23-40.4-51.66-52.66-82.82-6.13-15.58-10.82-31.82-14-48.37a282.465 282.465 0 0 1-4.79-50.41c-.17-33.99 5.91-68.3 18.16-100.31 19.81-52.07 54.48-96.38 98.56-127.59 10.96-7.88 22.65-14.77 34.74-20.84l4.55-2.25 2.28-1.12 2.33-1.02 9.32-4.06c6.34-2.42 12.64-4.96 19.15-6.92 6.43-2.21 13.05-3.83 19.64-5.58 6.65-1.43 13.38-2.99 19.9-3.95 3.29-.55 6.57-1.13 9.92-1.49 3.33-.4 6.66-.86 10.02-1.2 3.37-.27 6.75-.54 10.13-.82 1.69-.16 3.39-.21 5.1-.28l5.12-.19c17.97-.57 36.1.64 53.81 3.31 8.12 1.22 16.2 2.74 24.21 4.53 3.97.89 7.92 1.84 11.85 2.84 1.95.5 3.9 1.02 5.85 1.54 2 .54 4.3-1.13 2.15-2.94-1.9-1.6-6.69-2.24-8.12-2.64a299.76 299.76 0 0 0-12.88-3.28c-8.59-1.99-17.26-3.6-25.98-4.83-17.27-2.44-34.74-3.4-52.17-2.83-.87.03-1.73.06-2.6.1-4.46.08-8.86.5-13.29.82-4.44.26-8.8.88-13.2 1.39-4.41.45-8.73 1.24-13.09 1.94-4.31.62-8.79 1.63-13.22 2.56-17.74 3.86-35.19 9.51-51.91 16.91-33.48 14.77-63.89 36.58-88.75 63.38-24.91 26.78-44.34 58.53-56.82 92.73-11.88 32.55-17.81 67.37-17.42 101.94a289.77 289.77 0 0 0 5.16 51.29c3.29 16.85 8.16 33.41 14.48 49.28 12.63 31.77 31.29 60.68 54.18 84.3 22.9 23.62 49.91 42.03 78.56 53.97 1.54.63 2.64.47 2.38-.34-.66-.94-2.58-2.36-4.09-2.98z" class="st0"></path><path d="M289.36 546.05c-.52-.45-1.14-.83-1.73-1.01-11.45-3.93-22.79-10.41-33.12-18.94-10.27-8.59-19.45-19.24-27.01-31.16-1.01-1.45-1.82-3.03-2.72-4.55-.87-1.54-1.79-3.06-2.63-4.62l-2.38-4.78c-.8-1.59-1.61-3.18-2.27-4.84-1.4-3.28-2.87-6.54-4-9.94l-1.81-5.05-1.54-5.14c-2.08-6.84-3.52-13.87-4.76-20.87-.54-3.52-.96-7.07-1.43-10.57l-.42-5.27c-.12-1.75-.36-3.49-.37-5.24-.1-2.68-1.04-4.94-2.2-5.19-1.18-.07-2.06 1.89-2.06 4.77.02.92 0 1.84.06 2.76l.19 2.77c.13 1.85.25 3.7.38 5.56.15 1.86.44 3.71.66 5.57.25 1.85.42 3.74.74 5.58 1.29 7.38 2.8 14.79 5.08 21.97 4.37 14.41 10.73 28.25 19.06 40.49 16.33 24.71 40.24 42.8 63.98 50.24 1.34.27 1.89-.27 1.48-1.17-.2-.46-.65-.94-1.18-1.37z" class="st0"></path></svg></a></div><div class="MuiBox-root optizmo-g4cabq" id="overview-tour-appbar-nav-links"><div class="optizmo-jqmvss-globalSearchWrapper"><div class="optizmo-1cj65as-searchIconWrapper"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-hf1y26-searchIcon" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="SearchIcon"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"></path></svg></div><input type="text" aria-label="Search SUPPRESS" class="optizmo-nzf6jq-searchBar-fullBorder" placeholder="Search SUPPRESS" autocomplete="off" spellcheck="false" data-testid="global-search-input--desktop" value=""></div><div data-testid="settings-menu"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeLarge optizmo-1n6pr23" tabindex="0" type="button" title="Settings" aria-label="Settings menu" data-testid="settings-menu__launch"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="SettingsOutlinedIcon"><path d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.09-.16-.26-.25-.44-.25-.06 0-.12.01-.17.03l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1q-.09-.03-.18-.03c-.17 0-.34.09-.43.25l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.09.16.26.25.44.25.06 0 .12-.01.17-.03l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1q.09.03.18.03c.17 0 .34-.09.43-.25l2-3.46c.12-.22.07-.49-.12-.64zm-1.98-1.71c.04.31.05.52.05.73s-.02.43-.05.73l-.14 1.13.89.7 1.08.84-.7 1.21-1.27-.51-1.04-.42-.9.68c-.43.32-.84.56-1.25.73l-1.06.43-.16 1.13-.2 1.35h-1.4l-.19-1.35-.16-1.13-1.06-.43c-.43-.18-.83-.41-1.23-.71l-.91-.7-1.06.43-1.27.51-.7-1.21 1.08-.84.89-.7-.14-1.13c-.03-.31-.05-.54-.05-.74s.02-.43.05-.73l.14-1.13-.89-.7-1.08-.84.7-1.21 1.27.51 1.04.42.9-.68c.43-.32.84-.56 1.25-.73l1.06-.43.16-1.13.2-1.35h1.39l.19 1.35.16 1.13 1.06.43c.43.18.83.41 1.23.71l.91.7 1.06-.43 1.27-.51.7 1.21-1.07.85-.89.7zM12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4m0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2"></path></svg></button></div><div data-testid="help-menu"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeLarge optizmo-1n6pr23" tabindex="0" type="button" title="Help" aria-label="Help menu" data-testid="help-menu__launch"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="HelpOutlineOutlinedIcon"><path d="M11 18h2v-2h-2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4"></path></svg></button></div><div data-testid="user-profile-menu"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeLarge optizmo-qgfbz7" tabindex="0" type="button" title="Account" aria-label="User account menu" data-testid="user-profile-menu__launch"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="AccountCircleOutlinedIcon"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2M7.35 18.5C8.66 17.56 10.26 17 12 17s3.34.56 4.65 1.5c-1.31.94-2.91 1.5-4.65 1.5s-3.34-.56-4.65-1.5m10.79-1.38C16.45 15.8 14.32 15 12 15s-4.45.8-6.14 2.12C4.7 15.73 4 13.95 4 12c0-4.42 3.58-8 8-8s8 3.58 8 8c0 1.95-.7 3.73-1.86 5.12"></path><path d="M12 6c-1.93 0-3.5 1.57-3.5 3.5S10.07 13 12 13s3.5-1.57 3.5-3.5S13.93 6 12 6m0 5c-.83 0-1.5-.67-1.5-1.5S11.17 8 12 8s1.5.67 1.5 1.5S12.83 11 12 11"></path></svg></button></div></div></div></header></div><nav class="optizmo-1yuhazq-root"><div class="MuiDrawer-root MuiDrawer-docked optizmo-oms1ax" data-testid="desktop-drawer"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-elevation6 MuiDrawer-paper MuiDrawer-paperAnchorLeft MuiDrawer-paperAnchorDockedLeft optizmo-1tmng92-drawerPaper" style="--Paper-shadow: 0px 8px 16px 0px rgba(82,63,105,0.10);"><div class="optizmo-1qdxde7-drawerHeader"><h6 class="MuiTypography-root MuiTypography-h6 optizmo-1fw7f5w-h6" data-testid="suppress-drawer_app-title" style="margin-bottom: 0px;">SUPPRESS</h6><p class="MuiTypography-root MuiTypography-body2 MuiTypography-noWrap optizmo-18akxiw-body2" title="Bizaglo LLC" data-testid="suppress-drawer_app-subtitle" style="margin-bottom: 0px;">Bizaglo LLC</p></div><div class="optizmo-fpki9m-drawerContent"><ul class="MuiList-root MuiList-padding optizmo-1jq6ipv-listPadding" id="insights-menu"><li class="MuiListSubheader-root MuiListSubheader-gutters optizmo-kxhcls-root" data-testid="insights-menu__subheader"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Insights</p></li><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" href="/insights"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="HomeOutlinedIcon"><path d="m12 5.69 5 4.5V18h-2v-6H9v6H7v-7.81zM12 3 2 12h3v8h6v-6h2v6h6v-8h3z"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Overview</p></div></a><div id="overview-tour-compliance-links"><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" href="/insights/suppression-compliance"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="VerifiedUserOutlinedIcon"><path d="M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5zm7 10c0 4.52-2.98 8.69-7 9.93-4.02-1.24-7-5.41-7-9.93V6.3l7-3.11 7 3.11zm-11.59.59L6 13l4 4 8-8-1.41-1.42L10 14.17z"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Suppression Compliance</p></div></a><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" id="product-tour-affiliate-compliance-link" href="/insights/affiliate-compliance"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="PolicyOutlinedIcon"><path d="M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5zm7 10c0 1.85-.51 3.65-1.38 5.21l-1.45-1.45c1.29-1.94 1.07-4.58-.64-6.29-1.95-1.95-5.12-1.95-7.07 0s-1.95 5.12 0 7.07c1.71 1.71 4.35 1.92 6.29.64l1.72 1.72c-1.19 1.42-2.73 2.51-4.47 3.04-4.02-1.25-7-5.42-7-9.94V6.3l7-3.11 7 3.11zm-7 4c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Affiliate Compliance</p></div></a></div><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" href="/insights/brand-integrity"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ViewInArOutlinedIcon"><path d="M3 4c0-.55.45-1 1-1h2V1H4C2.34 1 1 2.34 1 4v2h2zm0 16v-2H1v2c0 1.66 1.34 3 3 3h2v-2H4c-.55 0-1-.45-1-1M20 1h-2v2h2c.55 0 1 .45 1 1v2h2V4c0-1.66-1.34-3-3-3m1 19c0 .55-.45 1-1 1h-2v2h2c1.66 0 3-1.34 3-3v-2h-2zm-2-5.13V9.13c0-.72-.38-1.38-1-1.73l-5-2.88c-.31-.18-.65-.27-1-.27s-.69.09-1 .27L6 7.39c-.62.36-1 1.02-1 1.74v5.74c0 .72.38 1.38 1 1.73l5 2.88c.31.18.65.27 1 .27s.69-.09 1-.27l5-2.88c.62-.35 1-1.01 1-1.73m-8 2.3-4-2.3v-4.63l4 2.33zm1-6.33L8.04 8.53 12 6.25l3.96 2.28zm5 4.03-4 2.3v-4.6l4-2.33z"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Brand Integrity</p><span class="MuiBox-root optizmo-lhnfv9-listItemTag"><span class="MuiBox-root optizmo-1rydtyo-root"><span class="MuiBox-root optizmo-2svbdb-tagContainer-colorPrimary"><span class="MuiBox-root optizmo-5xn4s6-content-contentColorPrimary">New</span></span></span></span></div></a><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1k6zz1o-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" id="overview-tour-optout-redirect-link" href="/insights/optout-redirect-credits"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="MonetizationOnOutlinedIcon"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Opt-Out Redirect Credits</p></div></a></ul><div class="MuiBox-root optizmo-1my9rcs-root-rootHorizontal-divider"><hr class="MuiDivider-root MuiDivider-fullWidth optizmo-1pr9iwp-divider-horizontalDivider"></div><ul class="MuiList-root MuiList-padding optizmo-1jq6ipv-listPadding"><li class="MuiListSubheader-root MuiListSubheader-gutters optizmo-kxhcls-root"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Configuration</p></li><div id="overview-tour-configuration-links"><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root Mui-selected MuiMenuItem-root Mui-selected optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" data-testid="app-drawer__link" href="/optouts.html"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="StorageIcon"><path d="M2 20h20v-4H2zm2-3h2v2H4zM2 4v4h20V4zm4 3H4V5h2zm-4 7h20v-4H2zm2-3h2v2H4z"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Opt-Out Lists</p></div></a><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" data-testid="app-drawer__link" href="/optouts.html&amp;s=Global"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="DnsIcon"><path d="M20 13H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1M7 19c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2M20 3H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V4c0-.55-.45-1-1-1M7 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Global Opt-Out List</p></div></a><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" data-testid="app-drawer__link" href="/transfer.html"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="LoopIcon"><path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8m0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4z"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Transfers</p></div></a><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" data-testid="app-drawer__link" href="/client.html&amp;s=List"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="StorefrontIcon"><path d="m21.9 8.89-1.05-4.37c-.22-.9-1-1.52-1.91-1.52H5.05c-.9 0-1.69.63-1.9 1.52L2.1 8.89c-.24 1.02-.02 2.06.62 2.88.08.11.19.19.28.29V19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-6.94c.09-.09.2-.18.28-.28.64-.82.87-1.87.62-2.89m-2.99-3.9 1.05 4.37c.1.42.01.84-.25 1.17-.14.18-.44.47-.94.47-.61 0-1.14-.49-1.21-1.14L16.98 5zM13 5h1.96l.54 4.52c.05.39-.07.78-.33 1.07-.22.26-.54.41-.95.41-.67 0-1.22-.59-1.22-1.31zM8.49 9.52 9.04 5H11v4.69c0 .72-.55 1.31-1.29 1.31-.34 0-.65-.15-.89-.41-.25-.29-.37-.68-.33-1.07m-4.45-.16L5.05 5h1.97l-.58 4.86c-.08.65-.6 1.14-1.21 1.14-.49 0-.8-.29-.93-.47-.27-.32-.36-.75-.26-1.17M5 19v-6.03c.08.01.15.03.23.03.87 0 1.66-.36 2.24-.95.6.6 1.4.95 2.31.95.87 0 1.65-.36 2.23-.93.59.57 1.39.93 2.29.93.84 0 1.64-.35 2.24-.95.58.59 1.37.95 2.24.95.08 0 .15-.02.23-.03V19z"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Advertisers</p></div></a><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1qft4in-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" data-testid="app-drawer__link" href="/mailer.html"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="SendIcon"><path d="M2.01 21 23 12 2.01 3 2 10l15 2-15 2z"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Affiliates</p></div></a></div><div id="overview-tour-domain-link"><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone MuiButtonBase-root MuiMenuItem-root MuiMenuItem-root optizmo-1k6zz1o-listItemRoot-listItemRoot" tabindex="-1" role="menuitem" data-testid="app-drawer__link" href="/optoutdomain.html"><div class="MuiListItemIcon-root optizmo-h9aqss-listItemIcon"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium optizmo-kgthdo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="PublicIcon"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39"></path></svg></div><div class="MuiListItemText-root optizmo-tvzie2-listItemTextRoot"><p class="MuiTypography-root MuiTypography-body2 optizmo-1w73c6y-body2" style="margin-bottom: 0px;">Opt-Out Domains</p></div></a></div></ul></div><div class="optizmo-cdwfpe-drawerFooter"><hr class="MuiDivider-root MuiDivider-fullWidth optizmo-1njvr8c-drawerFooterDivider"><span class="MuiTypography-root MuiTypography-caption optizmo-mpvuvi-caption">© OPTIZMO Technologies, LLC</span><ul class="optizmo-1c4aw49-root"><li class="optizmo-1pefcw6-item"><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone optizmo-1hjebco-link" rel="noopener noreferrer" href="https://www.optizmo.com/optizmo-terms-of-service/" target="_blank">Terms of Service</a></li><li class="optizmo-1pefcw6-item"><a class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone optizmo-1hjebco-link" rel="noopener noreferrer" href="https://www.optizmo.com/privacy/" target="_blank">Privacy Policy</a></li></ul></div></div></div></nav></div></div>
    

    <div id="container" class="clearfix has-react-nav">
      <!-- Conditionally apply the "content" CSS class which adds padding -->
      <div class="clearfix content">

        <div id="suppress-react-root"></div>


<!-- IDs: mailers = mailer-chart ; optouts = optouts-chart -->


<script src="/media/js/vendor/mootools-adapter.js"></script>
<script src="/media/js/vendor/highstock.js"></script>

<script type="text/javascript">
window.addEvent('domready', function() {
Highcharts.Point.prototype.tooltipFormatter = function (useHeader) {
    var point = this, series = point.series;
    return ['<span style="color:' + series.color + '">', (point.name || series.name), '</span>: ',
        (!useHeader ? ('<b>x = ' + (point.name || point.x) + ',</b> ') : ''),
        '<b>', (!useHeader ? 'y = ' : ''), Highcharts.numberFormat(point.y, 0), '</b>'].join('');
};


	if( $('optoutschart') ) {
		window.chart = new Highcharts.StockChart({
		    chart: { renderTo: 'optoutschart', selectionMarkerFill: '#fc9515', height: 250 },
		    rangeSelector: { selected: 1 },
		    plotOptions: {
		    	area: {
					fillColor : 'rgba(242, 149, 21, 0.4)', color: '#fc9515', lineWidth: '4',
		    		states: {
		    			hover: { enabled: true, lineWidth: 4 }
		    		}
		    	}
		    },
		    title: false,
		    yAxis: { gridLineColor: '#f5e1aa', min: 0 },
		    xAxis: { gridLineColor: '#f5e1aa', gridLineWidth: 1, range: 30 * 24 * 3600 * 1000 },
		    navigation: {
		    	buttonOptions: { backgroundColor: '#ccc' }
		    },
		    rangeSelector: {
		    	enabled: false,
		    	selected: 1
		    },
		    navigator: { maskFill: 'rgba(155, 155, 155, 0.3)', outlineColor: '#ccc', outlineWidth: 2,
		    	series: {
		    		lineWidth: 0
		    	}
		    },
		    scrollbar: { barBackgroundColor: '#ccc', barBorderRadius: 0, barBorderWidth: 0, buttonBackgroundColor: '#ccc', buttonBorderWidth: 0, buttonBorderRadius: 0, trackBackgroundColor: 'none', trackBorderWidth: 1, trackBorderRadius: 8, trackBorderColor: '#ccc', rifleColor: '#fff', buttonArrowColor: '#fff' },
		    series: [{
		        name: 'Opt-Outs',
		        data: [[1717497393000,0],[1717583793000,46],[1717670193000,13],[1717756593000,5],[1717842993000,0],[1717929393000,1],[1718015793000,1],[1718102193000,2],[1718188593000,0],[1718274993000,0],[1718361393000,0],[1718447793000,0],[1718534193000,0],[1718620593000,0],[1718706993000,0],[1718793393000,0],[1718879793000,0],[1718966193000,0],[1719052593000,0],[1719138993000,0],[1719225393000,0],[1719311793000,0],[1719398193000,0],[1719484593000,0],[1719570993000,0],[1719657393000,0],[1719743793000,0],[1719830193000,0],[1719916593000,0],[1720002993000,0],[1720089393000,0],[1720175793000,6],[1720262193000,2],[1720348593000,1],[1720434993000,0],[1720521393000,0],[1720607793000,0],[1720694193000,0],[1720780593000,0],[1720866993000,0],[1720953393000,0],[1721039793000,0],[1721126193000,0],[1721212593000,0],[1721298993000,0],[1721385393000,0],[1721471793000,0],[1721558193000,0],[1721644593000,0],[1721730993000,0],[1721817393000,0],[1721903793000,0],[1721990193000,0],[1722076593000,0],[1722162993000,0],[1722249393000,0],[1722335793000,0],[1722422193000,0],[1722508593000,0],[1722594993000,0],[1722681393000,0],[1722767793000,0],[1722854193000,0],[1722940593000,0],[1723026993000,0],[1723113393000,0],[1723199793000,0],[1723286193000,0],[1723372593000,0],[1723458993000,0],[1723545393000,0],[1723631793000,0],[1723718193000,0],[1723804593000,0],[1723890993000,0],[1723977393000,0],[1724063793000,0],[1724150193000,0],[1724236593000,0],[1724322993000,0],[1724409393000,0],[1724495793000,0],[1724582193000,0],[1724668593000,0],[1724754993000,0],[1724841393000,0],[1724927793000,0],[1725014193000,0],[1725100593000,3],[1725186993000,2],[1725273393000,0],[1725359793000,0],[1725446193000,1],[1725532593000,0],[1725618993000,0],[1725705393000,0],[1725791793000,0],[1725878193000,0],[1725964593000,0],[1726050993000,1],[1726137393000,0],[1726223793000,0],[1726310193000,0],[1726396593000,0],[1726482993000,0],[1726569393000,0],[1726655793000,0],[1726742193000,0],[1726828593000,0],[1726914993000,0],[1727001393000,0],[1727087793000,0],[1727174193000,0],[1727260593000,0],[1727346993000,0],[1727433393000,0],[1727519793000,0],[1727606193000,0],[1727692593000,0],[1727778993000,0],[1727865393000,0],[1727951793000,0],[1728038193000,0],[1728124593000,0],[1728210993000,0],[1728297393000,0],[1728383793000,0],[1728470193000,0],[1728556593000,0],[1728642993000,0],[1728729393000,0],[1728815793000,0],[1728902193000,0],[1728988593000,61],[1729074993000,46],[1729161393000,9],[1729247793000,6],[1729334193000,3],[1729420593000,5],[1729506993000,5],[1729593393000,1],[1729679793000,1],[1729766193000,2],[1729852593000,1],[1729938993000,0],[1730025393000,2],[1730111793000,2],[1730198193000,3],[1730284593000,4],[1730370993000,1],[1730457393000,0],[1730543793000,0],[1730630193000,0],[1730716593000,0],[1730802993000,1],[1730889393000,0],[1730975793000,0],[1731062193000,0],[1731148593000,0],[1731234993000,0],[1731321393000,0],[1731407793000,0],[1731494193000,0],[1731580593000,0],[1731666993000,0],[1731753393000,0],[1731839793000,0],[1731926193000,0],[1732012593000,0],[1732098993000,0],[1732185393000,0],[1732271793000,0],[1732358193000,0],[1732444593000,0],[1732530993000,0],[1732617393000,0],[1732703793000,0],[1732790193000,0],[1732876593000,0],[1732962993000,0],[1733049393000,1],[1733135793000,0],[1733222193000,0],[1733308593000,0],[1733394993000,0],[1733481393000,0],[1733567793000,0],[1733654193000,0],[1733740593000,0],[1733826993000,0],[1733913393000,0],[1733999793000,0],[1734086193000,0],[1734172593000,0],[1734258993000,0],[1734345393000,0],[1734431793000,0],[1734518193000,0],[1734604593000,0],[1734690993000,0],[1734777393000,0],[1734863793000,0],[1734950193000,0],[1735036593000,0],[1735122993000,0],[1735209393000,0],[1735295793000,0],[1735382193000,0],[1735468593000,0],[1735554993000,0],[1735641393000,0],[1735727793000,0],[1735814193000,0],[1735900593000,0],[1735986993000,0],[1736073393000,0],[1736159793000,0],[1736246193000,0],[1736332593000,0],[1736418993000,0],[1736505393000,0],[1736591793000,0],[1736678193000,0],[1736764593000,0],[1736850993000,0],[1736937393000,0],[1737023793000,0],[1737110193000,0],[1737196593000,0],[1737282993000,0],[1737369393000,0],[1737455793000,0],[1737542193000,0],[1737628593000,0],[1737714993000,0],[1737801393000,0],[1737887793000,0],[1737974193000,0],[1738060593000,0],[1738146993000,0],[1738233393000,0],[1738319793000,0],[1738406193000,0],[1738492593000,0],[1738578993000,0],[1738665393000,0],[1738751793000,0],[1738838193000,0],[1738924593000,0],[1739010993000,0],[1739097393000,0],[1739183793000,0],[1739270193000,0],[1739356593000,0],[1739442993000,0],[1739529393000,0],[1739615793000,0],[1739702193000,0],[1739788593000,0],[1739874993000,0],[1739961393000,0],[1740047793000,0],[1740134193000,0],[1740220593000,0],[1740306993000,0],[1740393393000,0],[1740479793000,0],[1740566193000,0],[1740652593000,0],[1740738993000,0],[1740825393000,0],[1740911793000,0],[1740998193000,0],[1741084593000,0],[1741170993000,0],[1741257393000,0],[1741343793000,0],[1741430193000,0],[1741516593000,0],[1741602993000,0],[1741689393000,0],[1741775793000,0],[1741862193000,0],[1741948593000,0],[1742034993000,0],[1742121393000,0],[1742207793000,0],[1742294193000,0],[1742380593000,0],[1742466993000,0],[1742553393000,0],[1742639793000,0],[1742726193000,0],[1742812593000,0],[1742898993000,0],[1742985393000,0],[1743071793000,0],[1743158193000,0],[1743244593000,0],[1743330993000,0],[1743417393000,0],[1743503793000,0],[1743590193000,0],[1743676593000,0],[1743762993000,0],[1743849393000,0],[1743935793000,0],[1744022193000,0],[1744108593000,0],[1744194993000,0],[1744281393000,0],[1744367793000,0],[1744454193000,0],[1744540593000,0],[1744626993000,0],[1744713393000,0],[1744799793000,0],[1744886193000,0],[1744972593000,0],[1745058993000,0],[1745145393000,0],[1745231793000,0],[1745318193000,0],[1745404593000,0],[1745490993000,0],[1745577393000,0],[1745663793000,0],[1745750193000,0],[1745836593000,0],[1745922993000,0],[1746009393000,0],[1746095793000,0],[1746182193000,0],[1746268593000,0],[1746354993000,0],[1746441393000,0],[1746527793000,0],[1746614193000,0],[1746700593000,0],[1746786993000,0],[1746873393000,0],[1746959793000,0],[1747046193000,0],[1747132593000,0],[1747218993000,0],[1747305393000,0],[1747391793000,0],[1747478193000,0],[1747564593000,0],[1747650993000,0],[1747737393000,0],[1747823793000,0],[1747910193000,0],[1747996593000,0],[1748082993000,0],[1748169393000,0],[1748255793000,0],[1748342193000,0],[1748428593000,0],[1748514993000,0]],
				type : 'area',
				marker: {
					radius: 4,
					lineWidth: 2
				},
				threshold : null,
		        tooltip: {
		        	valueDecimals: 2
		        }
		    }]
		});
	}



});
</script>



<ul class="crumbsNav clearfix">
 <li class="sgl start"><a href="/">Home</a></li>
 
  <li><a href="client.html&amp;ia=d6ac2d6c5d03bc9a&amp;s=View">Neptune Ads<dfn>Advertiser</dfn></a></li>
 
 <li class="end"><span>Ace Hardware at Bizaglo<dfn>Opt-Out List</dfn></span></li>
</ul>


  <div id="contentMenu"><div class="contentMenuBody"><div class="primary"><a href="optouts.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=Edit" class="first">Edit Opt-Out List</a></div><div style="margin: 0px; position: relative; overflow: hidden; height: 0px; box-shadow: rgba(82, 63, 105, 0.15) 0px 0px 50px; z-index: 1;"><div class="secondary" style="margin: -251px 0px 0px; overflow: hidden; position: static;"><a href="optouts.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=AddEmails">Add Emails</a><a href="transfer.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=Create">Create a Transfer</a><a href="optouts.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=Delete" class="delete['Opt-Out List']">Delete Opt-Out List</a><a href="optouts.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=Download">Download List</a><a href="javascript:void(0)" class="" title="" data-add-email-aliases-link="">
          Add Email Aliases
        </a></div></div><div class="dropButton"></div></div>
    
      
      
      
      
      

      
        
      
    

    
    
    
  </div>

  <!-- Content for the "Add Email Aliases" modal. -->
  <template id="add-email-aliases-modal-content">
  <p>
    Email Aliases will be added to this Opt-Out List via an import and you will 
    be emailed once the import has completed.
  </p>
  <p>
    Adding Email Aliases will increase your list size by approximately 30%, but 
    the exact amount will vary depending on your exposure to alias domains and your
    existing alias coverage.
  </p>
  <p>
    For more information on Domain Alias Protection, visit the <a href="https://support.optizmo.com/hc/en-us/articles/22946868802579-What-is-Domain-Alias-Protection" target="_blank">Help Centre</a>.
  </p>

  

  

  <div>
    
      <a href="optouts.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=AddAliases" class="button">Add Email Aliases</a>
    
  </div>
</template>

<script>
window.addEvent('load', function() {
  const addEmailAliasesOverlayBox = new OverlayBox({
    autoOpen: false,
    heading: 'Add Email Aliases',
    content: $('add-email-aliases-modal-content').get('html')
  });

  function showAddEmailAliasesOverlay () {
    addEmailAliasesOverlayBox.open();
  }

  const addEmailAddressesLink = document.querySelector('[data-add-email-aliases-link]');
  if (addEmailAddressesLink) {
    addEmailAddressesLink.addEventListener('click', (event) => {
      event.preventDefault();
      showAddEmailAliasesOverlay();
    });
  }
});
</script>

  


<h1>List Profile <span class="small">for</span> <span>Ace Hardware at Bizaglo</span> </h1>



<div class="box2 boxDetails optoutView">
 <h3>Records</h3>
 <table><tbody><tr>
  <td><div class="count">1,044</div></td>
 </tr></tbody></table>
</div>



  <div class="box2 boxDetails boxAccess">
    <h3>Access Settings</h3>
    <table><tbody><tr>
     
       <td class="cleanse"><p title="Cleansing is allowed">Cleanse</p></td>
       <td class="md5"><p title="MD5 downloads are allowed">MD5</p></td>
       <td class=""><p title="SHA-512 downloads are NOT allowed">SHA</p></td>
       <td class=""><p title="Plain text downloads are NOT allowed">Plain</p></td>
     
    </tr></tbody></table>
    <p style="font-size: 10px; line-height: 125%">These settings are defined in the <strong>Advertiser</strong>. You can override in the Opt-Out List if needed.</p>
    <!--<p class="textRight"><a href="client.html&amp;ia=d6ac2d6c5d03bc9a&amp;s=Edit#Access">Change Access Settings</a></p>-->
   </div>



<div class="box2 boxDetails">
  <h3>Opt-Out List Details</h3>
  
  
  

  <table>
    <tbody><tr><th>Created :</th><td>Dec 15, 2023</td></tr>
    
    
      <tr><th>Handle Opt-Outs :</th><td>Use Optizmo Opt-Out page<br><em>Advertiser Settings</em></td></tr>
    
    <tr><th>Downloaded :</th><td>73 times</td></tr>
    <tr><th>Cleansed :</th><td>1 times</td></tr>
    <tr><th>Last Accessed :</th><td>Oct 19, 24</td></tr>
    
    
    
    
    
    
    
    
      <tr><th>Opt-Out List Id :</th><td>4628f1744a3c6682</td></tr>  
    
  </tbody></table>
  
    <p class="boxButtons"><a href="https://docs.optizmo.com">Manage with the OPTIZMO API</a></p>
  
</div>



<!-- LashBack Box -->
<div id="lashbackAdvert"></div>








<!-- Progress Container -->


<!-- Chart -->
<div class="box1">
 <h2 class="smaller">Daily Opt-Outs</h2>
 <div id="optoutschart" data-highcharts-chart="0"><div class="highcharts-container" id="highcharts-0" style="position: relative; overflow: hidden; width: 628px; height: 250px; text-align: left; line-height: normal; z-index: 0; font-family: &quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif; font-size: 12px;"><svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="628" height="250"><defs><clipPath id="highcharts-1"><rect fill="none" x="0" y="0" width="608" height="148"></rect></clipPath></defs><rect rx="5" ry="5" fill="#FFFFFF" x="0" y="0" width="628" height="250" stroke-width="0.000001"></rect><g transform="translate(10,222)"><rect fill="none" x="0" y="-0.5" width="608" height="14" stroke-width="1" stroke="#ccc" rx="8" ry="8"></rect><rect fill="#ccc" x="545" y="0" width="49" height="14" stroke-width="0.000001" stroke="#666" rx="0" ry="0"></rect><path fill="none" stroke="#fff" stroke-width="1" d="M 566 3.5 L 566 9.333333333333334 M 569 3.5 L 569 9.333333333333334 M 572 3.5 L 572 9.333333333333334" visibility="visible"></path><g><rect rx="0" ry="0" fill="#ccc" x="-1" y="-1" width="15" height="15" stroke-width="0.000001" stroke="#666"></rect><path fill="#fff" d="M 8 4 L 8 10 5 7"></path></g><g transform="translate(594,0)"><rect rx="0" ry="0" fill="#ccc" x="-1" y="-1" width="15" height="15" stroke-width="0.000001" stroke="#666"></rect><path fill="#fff" d="M 6 4 L 6 10 9 7"></path></g></g><g class="highcharts-grid" zIndex="1"><path fill="none" d="M 264.5 10 L 264.5 158" stroke="#f5e1aa" stroke-width="1" zIndex="1"></path><path fill="none" d="M 405.5 10 L 405.5 158" stroke="#f5e1aa" stroke-width="1" zIndex="1"></path><path fill="none" d="M 547.5 10 L 547.5 158" stroke="#f5e1aa" stroke-width="1" zIndex="1"></path><path fill="none" d="M 122.5 10 L 122.5 158" stroke="#f5e1aa" stroke-width="1" zIndex="1"></path></g><g class="highcharts-grid" zIndex="1"><path fill="none" d="M 10 84.5 L 618 84.5" stroke="#f5e1aa" stroke-width="1" zIndex="1"></path></g><g class="highcharts-grid" zIndex="1"><path fill="none" d="M 363.5 181 L 363.5 221" stroke="#C0C0C0" stroke-width="1" zIndex="1"></path><path fill="none" d="M 557.5 181 L 557.5 221" stroke="#C0C0C0" stroke-width="1" zIndex="1"></path><path fill="none" d="M 166.5 181 L 166.5 221" stroke="#C0C0C0" stroke-width="1" zIndex="1"></path></g><g class="highcharts-grid" zIndex="1"></g><g class="highcharts-axis" zIndex="2"><path fill="none" d="M 264.5 158 L 264.5 163" stroke="#C0D0E0" stroke-width="1"></path><path fill="none" d="M 405.5 158 L 405.5 163" stroke="#C0D0E0" stroke-width="1"></path><path fill="none" d="M 547.5 158 L 547.5 163" stroke="#C0D0E0" stroke-width="1"></path><path fill="none" d="M 122.5 158 L 122.5 163" stroke="#C0D0E0" stroke-width="1"></path><path fill="none" d="M 10 158.5 L 618 158.5" stroke="#C0D0E0" stroke-width="1" zIndex="7" visibility="visible"></path></g><g class="highcharts-axis" zIndex="2"></g><g class="highcharts-axis" zIndex="2"></g><g class="highcharts-axis" zIndex="2"></g><g class="highcharts-series-group" zIndex="3"><g class="highcharts-series" visibility="visible" zIndex="0.1" transform="translate(10,10)" clip-path="url(#highcharts-1)"><path fill="rgb(242,149,21)" d="M -20.266666666666666 74 L 0 74 L 20.266666666666666 74 L 40.53333333333333 74 L 60.800000000000004 74 L 81.06666666666666 74 L 101.33333333333333 74 L 121.60000000000001 74 L 141.86666666666667 74 L 162.13333333333333 74 L 182.4 74 L 202.66666666666666 74 L 222.93333333333334 74 L 243.20000000000002 74 L 263.4666666666667 74 L 283.73333333333335 74 L 304 74 L 324.26666666666665 74 L 344.53333333333336 74 L 364.8 74 L 385.06666666666666 74 L 405.3333333333333 74 L 425.6 74 L 445.8666666666667 74 L 466.1333333333333 74 L 486.40000000000003 74 L 506.6666666666667 74 L 526.9333333333334 74 L 547.2 74 L 567.4666666666667 74 L 587.7333333333333 74 L 608 74 L 608 148 L -20.266666666666666 148" fill-opacity="0.4" zIndex="0"></path><path fill="none" d="M -20.266666666666666 74 L 0 74 L 20.266666666666666 74 L 40.53333333333333 74 L 60.800000000000004 74 L 81.06666666666666 74 L 101.33333333333333 74 L 121.60000000000001 74 L 141.86666666666667 74 L 162.13333333333333 74 L 182.4 74 L 202.66666666666666 74 L 222.93333333333334 74 L 243.20000000000002 74 L 263.4666666666667 74 L 283.73333333333335 74 L 304 74 L 324.26666666666665 74 L 344.53333333333336 74 L 364.8 74 L 385.06666666666666 74 L 405.3333333333333 74 L 425.6 74 L 445.8666666666667 74 L 466.1333333333333 74 L 486.40000000000003 74 L 506.6666666666667 74 L 526.9333333333334 74 L 547.2 74 L 567.4666666666667 74 L 587.7333333333333 74 L 608 74" stroke="#fc9515" stroke-width="4" zIndex="1"></path></g><g class="highcharts-markers" visibility="visible" zIndex="0.1" transform="translate(10,10)" clip-path="none"></g><g class="highcharts-series" visibility="visible" zIndex="0.1" transform="translate(24,181)" clip-path="url(#highcharts-2)"><path fill="rgb(242,149,21)" d="M 0 36.7 C 0 36.7 0.969359331476323 11.5 1.615598885793872 11.5 C 2.2618384401114207 11.5 2.584958217270195 25.300000000000004 3.231197771587744 29.6 C 3.877437325905292 33.9 4.2005571030640665 32.48 4.846796657381616 33.9 C 5.493036211699165 35.32 5.816155988857939 36.7 6.462395543175488 36.7 C 7.1086350974930355 36.7 7.43175487465181 36.1 8.07799442896936 36.1 C 8.724233983286908 36.1 9.047353760445683 36.1 9.693593314763232 36.1 C 10.33983286908078 36.1 10.662952646239555 35.6 11.309192200557103 35.6 C 11.95543175487465 35.6 12.278551532033426 36.7 12.924791086350975 36.7 C 13.571030640668521 36.7 13.894150417827296 36.7 14.540389972144848 36.7 C 15.186629526462394 36.7 15.509749303621168 36.7 16.15598885793872 36.7 C 16.802228412256266 36.7 17.125348189415043 36.7 17.771587743732592 36.7 C 18.41782729805014 36.7 18.740947075208915 36.7 19.387186629526465 36.7 C 20.03342618384401 36.7 20.356545961002787 36.7 21.002785515320337 36.7 C 21.649025069637883 36.7 21.97214484679666 36.7 22.618384401114206 36.7 C 23.26462395543175 36.7 23.58774373259053 36.7 24.233983286908078 36.7 C 24.880222841225624 36.7 25.2033426183844 36.7 25.84958217270195 36.7 C 26.495821727019496 36.7 26.81894150417827 36.7 27.465181058495823 36.7 C 28.11142061281337 36.7 28.434540389972142 36.7 29.080779944289695 36.7 C 29.72701949860724 36.7 30.050139275766014 36.7 30.696378830083567 36.7 C 31.342618384401113 36.7 31.665738161559887 36.7 32.31197771587744 36.7 C 32.958217270194986 36.7 33.28133704735376 36.7 33.92757660167131 36.7 C 34.57381615598886 36.7 34.89693593314763 36.7 35.543175487465184 36.7 C 36.18941504178273 36.7 36.5125348189415 36.7 37.15877437325906 36.7 C 37.8050139275766 36.7 38.128133704735376 36.7 38.77437325905293 36.7 C 39.420612813370475 36.7 39.74373259052925 36.7 40.3899721448468 36.7 C 41.03621169916435 36.7 41.35933147632312 36.7 42.005571030640674 36.7 C 42.65181058495822 36.7 42.97493036211699 36.7 43.62116991643454 36.7 C 44.267409470752085 36.7 44.59052924791086 36.7 45.23676880222841 36.7 C 45.88300835654596 36.7 46.20612813370473 36.7 46.852367688022284 36.7 C 47.49860724233983 36.7 47.8217270194986 36.7 48.467966573816156 36.7 C 49.1142061281337 36.7 49.437325905292475 33.4 50.08356545961003 33.4 C 50.729805013927574 33.4 51.05292479108635 35.1 51.6991643454039 35.6 C 52.34540389972144 36.1 52.66852367688023 35.88 53.31476323119777 36.1 C 53.961002785515326 36.32000000000001 54.284122562674085 36.7 54.930362116991645 36.7 C 55.576601671309184 36.7 55.89972144846797 36.7 56.54596100278552 36.7 C 57.19220055710307 36.7 57.51532033426183 36.7 58.16155988857939 36.7 C 58.80779944289693 36.7 59.130919220055716 36.7 59.77715877437326 36.7 C 60.423398328690816 36.7 60.746518105849574 36.7 61.392757660167135 36.7 C 62.038997214484674 36.7 62.36211699164346 36.7 63.00835654596101 36.7 C 63.65459610027856 36.7 63.97771587743732 36.7 64.62395543175488 36.7 C 65.27019498607243 36.7 65.5933147632312 36.7 66.23955431754875 36.7 C 66.8857938718663 36.7 67.20891364902506 36.7 67.85515320334262 36.7 C 68.50139275766017 36.7 68.82451253481895 36.7 69.4707520891365 36.7 C 70.11699164345404 36.7 70.44011142061281 36.7 71.08635097493037 36.7 C 71.73259052924791 36.7 72.0557103064067 36.7 72.70194986072424 36.7 C 73.34818941504179 36.7 73.67130919220055 36.7 74.31754874651811 36.7 C 74.96378830083566 36.7 75.28690807799444 36.7 75.93314763231199 36.7 C 76.57938718662953 36.7 76.9025069637883 36.7 77.54874651810586 36.7 C 78.1949860724234 36.7 78.51810584958218 36.7 79.16434540389973 36.7 C 79.81058495821728 36.7 80.13370473537604 36.7 80.7799442896936 36.7 C 81.42618384401115 36.7 81.74930362116993 36.7 82.39554317548748 36.7 C 83.04178272980502 36.7 83.36490250696379 36.7 84.01114206128135 36.7 C 84.6573816155989 36.7 84.98050139275767 36.7 85.62674094707522 36.7 C 86.27298050139277 36.7 86.59610027855153 36.7 87.24233983286908 36.7 C 87.88857938718662 36.7 88.2116991643454 36.7 88.85793871866295 36.7 C 89.50417827298051 36.7 89.82729805013926 36.7 90.47353760445682 36.7 C 91.11977715877437 36.7 91.44289693593315 36.7 92.0891364902507 36.7 C 92.73537604456826 36.7 93.058495821727 36.7 93.70473537604457 36.7 C 94.35097493036211 36.7 94.6740947075209 36.7 95.32033426183844 36.7 C 95.966573816156 36.7 96.28969359331475 36.7 96.93593314763231 36.7 C 97.58217270194986 36.7 97.90529247910864 36.7 98.55153203342618 36.7 C 99.19777158774374 36.7 99.5208913649025 36.7 100.16713091922006 36.7 C 100.8133704735376 36.7 101.13649025069638 36.7 101.78272980501393 36.7 C 102.42896935933149 36.7 102.75208913649026 36.7 103.3983286908078 36.7 C 104.04456824512536 36.7 104.36768802228411 36.7 105.01392757660167 36.7 C 105.66016713091922 36.7 105.98328690807799 36.7 106.62952646239555 36.7 C 107.27576601671308 36.7 107.59888579387189 36.7 108.24512534818942 36.7 C 108.89136490250698 36.7 109.21448467966574 36.7 109.86072423398329 36.7 C 110.50696378830085 36.7 110.8300835654596 36.7 111.47632311977716 36.7 C 112.12256267409471 36.7 112.44568245125348 36.7 113.09192200557104 36.7 C 113.73816155988857 36.7 114.06128133704738 36.7 114.70752089136491 36.7 C 115.35376044568247 36.7 115.67688022284123 36.7 116.32311977715878 36.7 C 116.96935933147634 36.7 117.29247910863509 36.7 117.93871866295265 36.7 C 118.5849582172702 36.7 118.90807799442896 36.7 119.55431754874652 36.7 C 120.20055710306406 36.7 120.52367688022287 36.7 121.1699164345404 36.7 C 121.81615598885796 36.7 122.13927576601672 36.7 122.78551532033427 36.7 C 123.43175487465183 36.7 123.75487465181058 36.7 124.40111420612814 36.7 C 125.04735376044569 36.7 125.37047353760445 36.7 126.01671309192201 36.7 C 126.66295264623955 36.7 126.98607242339835 36.7 127.63231197771589 36.7 C 128.27855153203345 36.7 128.60167130919223 36.7 129.24791086350976 36.7 C 129.89415041782732 36.7 130.21727019498607 36.7 130.86350974930363 36.7 C 131.50974930362116 36.7 131.83286908077994 36.7 132.4791086350975 36.7 C 133.12534818941504 36.7 133.44846796657384 36.7 134.09470752089138 36.7 C 134.74094707520894 36.7 135.06406685236772 36.7 135.71030640668525 36.7 C 136.3565459610028 36.7 136.67966573816156 36.7 137.32590529247912 36.7 C 137.97214484679665 36.7 138.29526462395543 36.7 138.941504178273 36.7 C 139.58774373259052 36.7 139.91086350974933 36.7 140.55710306406687 36.7 C 141.20334261838443 36.7 141.5264623955432 35 142.17270194986074 35 C 142.8189415041783 35 143.14206128133705 35.25999999999999 143.7883008356546 35.6 C 144.43454038997214 35.94 144.75766016713092 36.7 145.40389972144848 36.7 C 146.050139275766 36.7 146.37325905292482 36.7 147.01949860724235 36.7 C 147.66573816155991 36.7 147.9888579387187 36.1 148.63509749303623 36.1 C 149.2813370473538 36.1 149.60445682451254 36.7 150.2506963788301 36.7 C 150.89693593314763 36.7 151.2200557103064 36.7 151.86629526462397 36.7 C 152.5125348189415 36.7 152.8356545961003 36.7 153.48189415041784 36.7 C 154.1281337047354 36.7 154.45125348189418 36.7 155.09749303621172 36.7 C 155.74373259052928 36.7 156.06685236768803 36.7 156.7130919220056 36.7 C 157.35933147632312 36.7 157.6824512534819 36.7 158.32869080779946 36.7 C 158.974930362117 36.7 159.2980501392758 36.1 159.94428969359333 36.1 C 160.5905292479109 36.1 160.91364902506967 36.7 161.5598885793872 36.7 C 162.20612813370477 36.7 162.52924791086352 36.7 163.17548746518108 36.7 C 163.8217270194986 36.7 164.1448467966574 36.7 164.79108635097495 36.7 C 165.43732590529248 36.7 165.7604456824513 36.7 166.40668523676882 36.7 C 167.05292479108638 36.7 167.37604456824516 36.7 168.0222841225627 36.7 C 168.66852367688026 36.7 168.991643454039 36.7 169.63788300835657 36.7 C 170.2841225626741 36.7 170.60724233983288 36.7 171.25348189415044 36.7 C 171.89972144846797 36.7 172.22284122562672 36.7 172.86908077994428 36.7 C 173.51532033426184 36.7 173.8384401114206 36.7 174.48467966573816 36.7 C 175.1309192200557 36.7 175.45403899721447 36.7 176.10027855153203 36.7 C 176.7465181058496 36.7 177.06963788300837 36.7 177.7158774373259 36.7 C 178.36211699164346 36.7 178.6852367688022 36.7 179.33147632311977 36.7 C 179.97771587743733 36.7 180.30083565459609 36.7 180.94707520891365 36.7 C 181.59331476323118 36.7 181.91643454038996 36.7 182.56267409470752 36.7 C 183.20891364902508 36.7 183.53203342618386 36.7 184.1782729805014 36.7 C 184.82451253481895 36.7 185.1476323119777 36.7 185.79387186629526 36.7 C 186.44011142061282 36.7 186.76323119777157 36.7 187.40947075208913 36.7 C 188.05571030640667 36.7 188.37883008356545 36.7 189.025069637883 36.7 C 189.67130919220057 36.7 189.99442896935935 36.7 190.64066852367688 36.7 C 191.28690807799444 36.7 191.6100278551532 36.7 192.25626740947075 36.7 C 192.9025069637883 36.7 193.22562674094706 36.7 193.87186629526462 36.7 C 194.51810584958216 36.7 194.84122562674094 36.7 195.4874651810585 36.7 C 196.13370473537606 36.7 196.45682451253484 36.7 197.10306406685237 36.7 C 197.74930362116993 36.7 198.07242339832868 36.7 198.71866295264624 36.7 C 199.3649025069638 36.7 199.68802228412255 36.7 200.3342618384401 36.7 C 200.98050139275765 36.7 201.30362116991643 36.7 201.949860724234 36.7 C 202.59610027855155 36.7 202.91922005571033 36.7 203.56545961002786 36.7 C 204.21169916434542 36.7 204.53481894150417 36.7 205.18105849582173 36.7 C 205.8272980501393 36.7 206.15041782729804 36.7 206.7966573816156 36.7 C 207.44289693593313 36.7 207.76601671309191 36.7 208.41225626740948 36.7 C 209.05849582172704 36.7 209.38161559888582 36.7 210.02785515320335 36.7 C 210.6740947075209 36.7 210.99721448467966 36.7 211.64345403899722 36.7 C 212.28969359331478 36.7 212.61281337047353 36.7 213.2590529247911 36.7 C 213.90529247910862 36.7 214.22841225626743 3.3 214.87465181058496 3.3 C 215.5208913649025 3.3 215.8440111420613 5.820000000000123 216.49025069637884 11.5 C 217.1364902506964 17.180000000000124 217.45961002785515 30 218.1058495821727 31.7 C 218.75208913649027 33.4 219.07520891364902 32.73999999999998 219.72144846796658 33.4 C 220.3676880222841 34.05999999999998 220.6908077994429 35 221.33704735376045 35 C 221.98328690807801 35 222.3064066852368 33.9 222.95264623955433 33.9 C 223.5988857938719 33.9 223.92200557103064 33.9 224.5682451253482 33.9 C 225.21448467966576 33.9 225.5376044568245 36.1 226.18384401114207 36.1 C 226.8300835654596 36.1 227.1532033426184 36.1 227.79944289693594 36.1 C 228.44568245125348 36.1 228.76880222841228 35.6 229.41504178272982 35.6 C 230.06128133704738 35.6 230.38440111420613 35.88 231.0306406685237 36.1 C 231.67688022284125 36.32000000000001 232 36.7 232.64623955431756 36.7 C 233.2924791086351 36.7 233.61559888579387 35.6 234.26183844011143 35.6 C 234.908077994429 35.6 235.23119777158777 35.6 235.8774373259053 35.6 C 236.52367688022287 35.6 236.84679665738162 35.22 237.49303621169918 35 C 238.13927576601674 34.78 238.4623955431755 34.5 239.10863509749305 34.5 C 239.75487465181058 34.5 240.0779944289694 35.660000000000004 240.72423398328692 36.1 C 241.37047353760445 36.540000000000006 241.69359331476326 36.7 242.3398328690808 36.7 C 242.98607242339835 36.7 243.3091922005571 36.7 243.95543175487467 36.7 C 244.60167130919223 36.7 244.92479108635098 36.7 245.57103064066854 36.7 C 246.21727019498607 36.7 246.54038997214485 36.7 247.1866295264624 36.7 C 247.83286908077997 36.7 248.15598885793875 36.1 248.80222841225628 36.1 C 249.44846796657384 36.1 249.7715877437326 36.7 250.41782729805016 36.7 C 251.06406685236772 36.7 251.38718662952647 36.7 252.03342618384403 36.7 C 252.67966573816156 36.7 253.00278551532037 36.7 253.6490250696379 36.7 C 254.29526462395543 36.7 254.61838440111424 36.7 255.26462395543177 36.7 C 255.91086350974933 36.7 256.2339832869081 36.7 256.88022284122565 36.7 C 257.5264623955432 36.7 257.84958217270196 36.7 258.4958217270195 36.7 C 259.1420612813371 36.7 259.46518105849583 36.7 260.1114206128134 36.7 C 260.75766016713095 36.7 261.0807799442897 36.7 261.72701949860726 36.7 C 262.3732590529248 36.7 262.6963788300836 36.7 263.34261838440113 36.7 C 263.9888579387187 36.7 264.31197771587745 36.7 264.958217270195 36.7 C 265.60445682451257 36.7 265.9275766016714 36.7 266.5738161559889 36.7 C 267.2200557103064 36.7 267.5431754874652 36.7 268.18941504178275 36.7 C 268.8356545961003 36.7 269.15877437325906 36.7 269.8050139275766 36.7 C 270.4512534818942 36.7 270.77437325905294 36.7 271.4206128133705 36.7 C 272.06685236768806 36.7 272.3899721448468 36.7 273.03621169916437 36.7 C 273.68245125348193 36.7 274.0055710306407 36.7 274.65181058495824 36.7 C 275.2980501392758 36.7 275.62116991643455 36.7 276.2674094707521 36.7 C 276.9136490250697 36.7 277.2367688022284 36.7 277.883008356546 36.7 C 278.52924791086355 36.7 278.85236768802235 36.7 279.49860724233986 36.7 C 280.14484679665736 36.7 280.46796657381617 36.7 281.11420612813373 36.7 C 281.7604456824513 36.7 282.08356545961004 36.7 282.7298050139276 36.7 C 283.37604456824516 36.7 283.6991643454039 36.7 284.3454038997215 36.7 C 284.99164345403904 36.7 285.3147632311978 36.7 285.96100278551535 36.7 C 286.6072423398329 36.7 286.93036211699166 36.7 287.5766016713092 36.7 C 288.2228412256268 36.7 288.54596100278553 36.7 289.1922005571031 36.7 C 289.83844011142065 36.7 290.1615598885794 36.1 290.80779944289696 36.1 C 291.4540389972145 36.1 291.77715877437333 36.7 292.42339832869084 36.7 C 293.06963788300834 36.7 293.39275766016715 36.7 294.0389972144847 36.7 C 294.68523676880227 36.7 295.008356545961 36.7 295.6545961002786 36.7 C 296.30083565459614 36.7 296.6239554317549 36.7 297.27019498607245 36.7 C 297.91643454039 36.7 298.23955431754877 36.7 298.8857938718663 36.7 C 299.5320334261839 36.7 299.85515320334264 36.7 300.5013927576602 36.7 C 301.14763231197776 36.7 301.4707520891365 36.7 302.11699164345407 36.7 C 302.76323119777163 36.7 303.0863509749304 36.7 303.73259052924794 36.7 C 304.3788300835655 36.7 304.7019498607243 36.7 305.3481894150418 36.7 C 305.9944289693593 36.7 306.3175487465181 36.7 306.9637883008357 36.7 C 307.61002785515325 36.7 307.933147632312 36.7 308.57938718662956 36.7 C 309.2256267409471 36.7 309.5487465181059 36.7 310.19498607242343 36.7 C 310.841225626741 36.7 311.16434540389974 36.7 311.8105849582173 36.7 C 312.45682451253487 36.7 312.7799442896936 36.7 313.4261838440112 36.7 C 314.07242339832874 36.7 314.3955431754875 36.7 315.04178272980505 36.7 C 315.6880222841226 36.7 316.01114206128136 36.7 316.6573816155989 36.7 C 317.3036211699165 36.7 317.6267409470753 36.7 318.2729805013928 36.7 C 318.9192200557103 36.7 319.2423398328691 36.7 319.88857938718667 36.7 C 320.5348189415042 36.7 320.857938718663 36.7 321.50417827298054 36.7 C 322.1504178272981 36.7 322.47353760445685 36.7 323.1197771587744 36.7 C 323.766016713092 36.7 324.0891364902507 36.7 324.7353760445683 36.7 C 325.38161559888584 36.7 325.7047353760446 36.7 326.35097493036216 36.7 C 326.9972144846797 36.7 327.32033426183847 36.7 327.96657381615603 36.7 C 328.6128133704736 36.7 328.93593314763234 36.7 329.5821727019499 36.7 C 330.22841225626746 36.7 330.55153203342627 36.7 331.1977715877438 36.7 C 331.8440111420613 36.7 332.1671309192201 36.7 332.81337047353765 36.7 C 333.4596100278552 36.7 333.78272980501396 36.7 334.4289693593315 36.7 C 335.0752089136491 36.7 335.39832869080783 36.7 336.0445682451254 36.7 C 336.69080779944295 36.7 337.0139275766017 36.7 337.66016713091926 36.7 C 338.3064066852368 36.7 338.6295264623956 36.7 339.27576601671313 36.7 C 339.9220055710307 36.7 340.24512534818945 36.7 340.891364902507 36.7 C 341.53760445682457 36.7 341.8607242339833 36.7 342.5069637883009 36.7 C 343.15320334261844 36.7 343.47632311977713 36.7 344.1225626740947 36.7 C 344.76880222841226 36.7 345.091922005571 36.7 345.73816155988857 36.7 C 346.3844011142061 36.7 346.7075208913649 36.7 347.35376044568244 36.7 C 348 36.7 348.3231197771587 36.7 348.9693593314763 36.7 C 349.6155988857939 36.7 349.9387186629526 36.7 350.5849582172702 36.7 C 351.23119777158774 36.7 351.55431754874655 36.7 352.20055710306406 36.7 C 352.84679665738156 36.7 353.16991643454037 36.7 353.81615598885793 36.7 C 354.4623955431755 36.7 354.78551532033424 36.7 355.4317548746518 36.7 C 356.0779944289694 36.7 356.4011142061281 36.7 357.0473537604457 36.7 C 357.69359331476323 36.7 358.016713091922 36.7 358.66295264623955 36.7 C 359.3091922005571 36.7 359.63231197771586 36.7 360.2785515320334 36.7 C 360.924791086351 36.7 361.2479108635097 36.7 361.8941504178273 36.7 C 362.54038997214485 36.7 362.8635097493036 36.7 363.50974930362116 36.7 C 364.1559888579387 36.7 364.47910863509753 36.7 365.12534818941504 36.7 C 365.77158774373254 36.7 366.09470752089135 36.7 366.7409470752089 36.7 C 367.38718662952647 36.7 367.7103064066852 36.7 368.3565459610028 36.7 C 369.0027855153204 36.7 369.3259052924791 36.7 369.97214484679665 36.7 C 370.6183844011142 36.7 370.94150417827296 36.7 371.5877437325905 36.7 C 372.2339832869081 36.7 372.55710306406684 36.7 373.2033426183844 36.7 C 373.84958217270196 36.7 374.17270194986065 36.7 374.81894150417827 36.7 C 375.46518105849583 36.7 375.7883008356546 36.7 376.43454038997214 36.7 C 377.0807799442897 36.7 377.4038997214485 36.7 378.050139275766 36.7 C 378.6963788300835 36.7 379.0194986072423 36.7 379.6657381615599 36.7 C 380.31197771587745 36.7 380.6350974930362 36.7 381.28133704735376 36.7 C 381.9275766016714 36.7 382.25069637883007 36.7 382.89693593314763 36.7 C 383.5431754874652 36.7 383.86629526462394 36.7 384.5125348189415 36.7 C 385.15877437325906 36.7 385.4818941504178 36.7 386.1281337047354 36.7 C 386.77437325905294 36.7 387.09749303621163 36.7 387.74373259052925 36.7 C 388.3899721448468 36.7 388.71309192200556 36.7 389.3593314763231 36.7 C 390.0055710306407 36.7 390.3286908077995 36.7 390.974930362117 36.7 C 391.6211699164345 36.7 391.9442896935933 36.7 392.59052924791087 36.7 C 393.2367688022284 36.7 393.5598885793872 36.7 394.20612813370474 36.7 C 394.85236768802235 36.7 395.17548746518105 36.7 395.8217270194986 36.7 C 396.46796657381617 36.7 396.7910863509749 36.7 397.4373259052925 36.7 C 398.08356545961004 36.7 398.4066852367688 36.7 399.05292479108635 36.7 C 399.6991643454039 36.7 400.0222841225626 36.7 400.6685236768802 36.7 C 401.3147632311978 36.7 401.63788300835654 36.7 402.2841225626741 36.7 C 402.93036211699166 36.7 403.25348189415047 36.7 403.899721448468 36.7 C 404.5459610027855 36.7 404.8690807799443 36.7 405.51532033426184 36.7 C 406.1615598885794 36.7 406.48467966573816 36.7 407.1309192200557 36.7 C 407.77715877437333 36.7 408.100278551532 36.7 408.7465181058496 36.7 C 409.39275766016715 36.7 409.7158774373259 36.7 410.36211699164346 36.7 C 411.008356545961 36.7 411.3314763231198 36.7 411.97771587743733 36.7 C 412.6239554317549 36.7 412.9470752089136 36.7 413.5933147632312 36.7 C 414.23955431754877 36.7 414.56267409470746 36.7 415.2089136490251 36.7 C 415.85515320334264 36.7 416.17827298050145 36.7 416.82451253481895 36.7 C 417.47075208913645 36.7 417.79387186629526 36.7 418.4401114206128 36.7 C 419.08635097493044 36.7 419.40947075208913 36.7 420.0557103064067 36.7 C 420.7019498607243 36.7 421.025069637883 36.7 421.67130919220057 36.7 C 422.3175487465181 36.7 422.6406685236769 36.7 423.28690807799444 36.7 C 423.933147632312 36.7 424.2562674094708 36.7 424.9025069637883 36.7 C 425.5487465181059 36.7 425.87186629526457 36.7 426.5181058495822 36.7 C 427.16434540389974 36.7 427.48746518105855 36.7 428.13370473537606 36.7 C 428.7799442896936 36.7 429.1030640668524 36.7 429.74930362116993 36.7 C 430.39554317548743 36.7 430.71866295264624 36.7 431.3649025069638 36.7 C 432.0111420612813 36.7 432.3342618384401 36.7 432.9805013927577 36.7 C 433.6267409470753 36.7 433.949860724234 36.7 434.59610027855155 36.7 C 435.24233983286905 36.7 435.56545961002786 36.7 436.2116991643454 36.7 C 436.857938718663 36.7 437.18105849582173 36.7 437.8272980501393 36.7 C 438.47353760445685 36.7 438.79665738161555 36.7 439.44289693593316 36.7 C 440.0891364902507 36.7 440.4122562674094 36.7 441.05849582172704 36.7 C 441.7047353760446 36.7 442.0278551532034 36.7 442.6740947075209 36.7 C 443.3203342618384 36.7 443.6434540389972 36.7 444.2896935933148 36.7 C 444.9359331476324 36.7 445.2590529247911 36.7 445.90529247910865 36.7 C 446.55153203342627 36.7 446.87465181058496 36.7 447.5208913649025 36.7 C 448.1671309192201 36.7 448.49025069637884 36.7 449.1364902506964 36.7 C 449.78272980501396 36.7 450.10584958217277 36.7 450.75208913649027 36.7 C 451.39832869080783 36.7 451.7214484679665 36.7 452.36768802228414 36.7 C 453.0139275766017 36.7 453.3370473537605 36.7 453.983286908078 36.7 C 454.6295264623956 36.7 454.9526462395544 36.7 455.5988857938719 36.7 C 456.2451253481894 36.7 456.5682451253482 36.7 457.21448467966576 36.7 C 457.86072423398326 36.7 458.18384401114207 36.7 458.83008356545963 36.7 C 459.47632311977725 36.7 459.79944289693594 36.7 460.4456824512535 36.7 C 461.091922005571 36.7 461.4150417827298 36.7 462.0612813370474 36.7 C 462.70752089136494 36.7 463.0306406685237 36.7 463.67688022284125 36.7 C 464.3231197771588 36.7 464.6462395543175 36.7 465.2924791086351 36.7 C 465.9387186629527 36.7 466.2618384401114 36.7 466.908077994429 36.7 C 467.55431754874655 36.7 467.87743732590536 36.7 468.52367688022287 36.7 C 469.16991643454037 36.7 469.4930362116992 36.7 470.13927576601674 36.7 C 470.78551532033435 36.7 471.10863509749305 36.7 471.7548746518106 36.7 C 472.4011142061282 36.7 472.7242339832869 36.7 473.3704735376045 36.7 C 474.01671309192204 36.7 474.3398328690808 36.7 474.98607242339835 36.7 C 475.6323119777159 36.7 475.9554317548747 36.7 476.6016713091922 36.7 C 477.2479108635098 36.7 477.5710306406685 36.7 478.2172701949861 36.7 C 478.86350974930366 36.7 479.18662952646247 36.7 479.83286908078 36.7 C 480.47910863509753 36.7 480.80222841225634 36.7 481.44846796657384 36.7 C 482.09470752089135 36.7 482.41782729805016 36.7 483.0640668523677 36.7 C 483.7103064066852 36.7 484.033426183844 36.7 484.6796657381616 36.7 C 485.3259052924792 36.7 485.6490250696379 36.7 486.29526462395546 36.7 C 486.94150417827296 36.7 487.2646239554318 36.7 487.91086350974933 36.7 C 488.5571030640669 36.7 488.88022284122565 36.7 489.5264623955432 36.7 C 490.17270194986077 36.7 490.49582172701946 36.7 491.1420612813371 36.7 C 491.78830083565464 36.7 492.11142061281333 36.7 492.75766016713095 36.7 C 493.4038997214485 36.7 493.7270194986073 36.7 494.3732590529248 36.7 C 495.0194986072423 36.7 495.34261838440113 36.7 495.9888579387187 36.7 C 496.6350974930363 36.7 496.958217270195 36.7 497.60445682451257 36.7 C 498.2506963788302 36.7 498.5738161559889 36.7 499.22005571030644 36.7 C 499.866295264624 36.7 500.18941504178275 36.7 500.8356545961003 36.7 C 501.4818941504179 36.7 501.8050139275767 36.7 502.4512534818942 36.7 C 503.09749303621174 36.7 503.42061281337044 36.7 504.06685236768806 36.7 C 504.7130919220056 36.7 505.0362116991644 36.7 505.68245125348193 36.7 C 506.3286908077995 36.7 506.6518105849583 36.7 507.2980501392758 36.7 C 507.9442896935933 36.7 508.2674094707521 36.7 508.9136490250697 36.7 C 509.5598885793872 36.7 509.883008356546 36.7 510.52924791086355 36.7 C 511.17548746518116 36.7 511.49860724233986 36.7 512.1448467966574 36.7 C 512.7910863509749 36.7 513.1142061281337 36.7 513.7604456824513 36.7 C 514.4066852367689 36.7 514.7298050139276 36.7 515.3760445682451 36.7 C 516.0222841225626 36.7 516.3454038997214 36.7 516.991643454039 36.7 C 517.6378830083565 36.7 517.9610027855153 36.7 518.6072423398329 36.7 C 519.2534818941505 36.7 519.5766016713093 36.7 520.2228412256268 36.7 C 520.8690807799443 36.7 521.192200557103 36.7 521.8384401114206 36.7 C 522.4846796657382 36.7 522.807799442897 36.7 523.4540389972145 36.7 C 524.100278551532 36.7 524.4233983286907 36.7 525.0696378830083 36.7 C 525.7158774373258 36.7 526.0389972144847 36.7 526.6852367688023 36.7 C 527.3314763231198 36.7 527.6545961002786 36.7 528.3008356545961 36.7 C 528.9470752089137 36.7 529.2701949860724 36.7 529.91643454039 36.7 C 530.5626740947075 36.7 530.8857938718663 36.7 531.5320334261838 36.7 C 532.1782729805014 36.7 532.5013927576601 36.7 533.1476323119778 36.7 C 533.7938718662953 36.7 534.1169916434541 36.7 534.7632311977716 36.7 C 535.4094707520892 36.7 535.732590529248 36.7 536.3788300835655 36.7 C 537.0250696378831 36.7 537.3481894150418 36.7 537.9944289693593 36.7 C 538.6406685236768 36.7 538.9637883008356 36.7 539.6100278551532 36.7 C 540.2562674094709 36.7 540.5793871866296 36.7 541.2256267409471 36.7 C 541.8718662952646 36.7 542.1949860724234 36.7 542.841225626741 36.7 C 543.4874651810585 36.7 543.8105849582173 36.7 544.4568245125348 36.7 C 545.1030640668524 36.7 545.4261838440112 36.7 546.0724233983287 36.7 C 546.7186629526462 36.7 547.0417827298049 36.7 547.6880222841226 36.7 C 548.3342618384402 36.7 548.657381615599 36.7 549.3036211699165 36.7 C 549.949860724234 36.7 550.2729805013927 36.7 550.9192200557103 36.7 C 551.5654596100278 36.7 551.8885793871866 36.7 552.5348189415042 36.7 C 553.1810584958217 36.7 553.5041782729805 36.7 554.150417827298 36.7 C 554.7966573816157 36.7 555.1197771587744 36.7 555.766016713092 36.7 C 556.4122562674095 36.7 556.7353760445683 36.7 557.3816155988858 36.7 C 558.0278551532034 36.7 558.3509749303621 36.7 558.9972144846797 36.7 C 559.6434540389972 36.7 559.966573816156 36.7 560.6128133704735 36.7 C 561.2590529247911 36.7 561.58217270195 36.7 562.2284122562675 36.7 C 562.8746518105851 36.7 563.1977715877438 36.7 563.8440111420613 36.7 C 564.4902506963788 36.7 564.8133704735376 36.7 565.4596100278552 36.7 C 566.1058495821728 36.7 566.4289693593315 36.7 567.075208913649 36.7 C 567.7214484679665 36.7 568.0445682451253 36.7 568.690807799443 36.7 C 569.3370473537605 36.7 569.6601671309193 36.7 570.3064066852368 36.7 C 570.9526462395544 36.7 571.2757660167132 36.7 571.9220055710307 36.7 C 572.5682451253482 36.7 572.8913649025069 36.7 573.5376044568245 36.7 C 574.1838440111421 36.7 574.5069637883009 36.7 575.1532033426184 36.7 C 575.799442896936 36.7 576.1225626740946 36.7 576.7688022284123 36.7 C 577.4150417827298 36.7 577.7381615598886 36.7 578.3844011142062 36.7 C 579.0306406685237 36.7 580 36.7 580 36.7 L 580 40 L 0 40" fill-opacity="0.4" zIndex="0"></path></g><g class="highcharts-markers" visibility="visible" zIndex="0.1" transform="translate(24,181)" clip-path="url(#highcharts-3)"></g></g><rect fill="rgb(155,155,155)" x="24" y="181" width="531" height="40" stroke-width="0.000001" fill-opacity="0.3" zIndex="3"></rect><rect fill="rgb(155,155,155)" x="604" y="181" width="0" height="40" stroke-width="0.000001" fill-opacity="0.3" zIndex="3"></rect><path fill="none" stroke-width="2" stroke="#ccc" zIndex="3" d="M 10 182 L 556 182 556 222 M 603 222 L 603 182 618 182"></path><g style="cursor:e-resize;" zIndex="3" transform="translate(605,193)"><rect rx="3" ry="3" fill="#FFF" x="-4.5" y="0.5" width="8" height="15" stroke-width="1" stroke="#666"></rect><path fill="#FFF" d="M -1.5 4 L -1.5 12 M 0.5 4 L 0.5 12" stroke="#666" stroke-width="1"></path></g><g style="cursor:e-resize;" zIndex="4" transform="translate(556,193)"><rect rx="3" ry="3" fill="#FFF" x="-4.5" y="0.5" width="8" height="15" stroke-width="1" stroke="#666"></rect><path fill="#FFF" d="M -1.5 4 L -1.5 12 M 0.5 4 L 0.5 12" stroke="#666" stroke-width="1"></path></g><g class="highcharts-axis-labels" zIndex="7"><text x="122.64114814814815" y="172" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;color:#666;line-height:14px;fill:#666;" text-anchor="middle"><tspan x="122.64114814814815">5. May</tspan></text><text x="264.5078148148148" y="172" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;color:#666;line-height:14px;fill:#666;" text-anchor="middle"><tspan x="264.5078148148148">12. May</tspan></text><text x="406.3744814814815" y="172" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;color:#666;line-height:14px;fill:#666;" text-anchor="middle"><tspan x="406.3744814814815">19. May</tspan></text><text x="548.2411481481481" y="172" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;color:#666;line-height:14px;fill:#666;" text-anchor="middle"><tspan x="548.2411481481481">26. May</tspan></text></g><g class="highcharts-axis-labels" zIndex="7"><text x="0" y="-9999" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;width:284px;color:#666;line-height:14px;fill:#666;" text-anchor="start"><tspan x="0">0</tspan></text></g><g class="highcharts-axis-labels" zIndex="7"><text x="169.8316962322245" y="217" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;color:#666;line-height:14px;fill:#666;" text-anchor="start"><tspan x="169.8316962322245">Sep '24</tspan></text><text x="366.93476029907686" y="217" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;color:#666;line-height:14px;fill:#666;" text-anchor="start"><tspan x="366.93476029907686">Jan '25</tspan></text><text x="560.8066265943415" y="217" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:11px;color:#666;line-height:14px;fill:#666;" text-anchor="start"><tspan x="560.8066265943415">May '25</tspan></text></g><g class="highcharts-axis-labels" zIndex="7"></g><g class="highcharts-tooltip" zIndex="8" style="padding:0;white-space:nowrap;" visibility="hidden"><rect rx="5" ry="5" fill="none" x="0" y="0" width="10" height="10" stroke-width="5" fill-opacity="0.85" isShadow="true" stroke="black" stroke-opacity="0.049999999999999996" transform="translate(1, 1)"></rect><rect rx="5" ry="5" fill="none" x="0" y="0" width="10" height="10" stroke-width="3" fill-opacity="0.85" isShadow="true" stroke="black" stroke-opacity="0.09999999999999999" transform="translate(1, 1)"></rect><rect rx="5" ry="5" fill="none" x="0" y="0" width="10" height="10" stroke-width="1" fill-opacity="0.85" isShadow="true" stroke="black" stroke-opacity="0.15" transform="translate(1, 1)"></rect><rect rx="5" ry="5" fill="rgb(255,255,255)" x="0" y="0" width="10" height="10" stroke-width="2" fill-opacity="0.85"></rect><text x="5" y="18" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:12px;color:#333333;fill:#333333;" zIndex="1"></text></g><text x="618" y="245" style="font-family:&quot;Lucida Grande&quot;, &quot;Lucida Sans Unicode&quot;, Verdana, Arial, Helvetica, sans-serif;font-size:10px;cursor:pointer;color:#909090;fill:#909090;" text-anchor="end" zIndex="8"><tspan x="618">Highcharts.com</tspan></text><g class="highcharts-tracker" zIndex="9"><g visibility="visible" zIndex="1" transform="translate(10,10)" clip-path="url(#highcharts-1)"><path fill="none" d="M -30.266666666666666 74 L -20.266666666666666 74 L 0 74 L 20.266666666666666 74 L 40.53333333333333 74 L 60.800000000000004 74 L 81.06666666666666 74 L 101.33333333333333 74 L 121.60000000000001 74 L 141.86666666666667 74 L 162.13333333333333 74 L 182.4 74 L 202.66666666666666 74 L 222.93333333333334 74 L 243.20000000000002 74 L 263.4666666666667 74 L 283.73333333333335 74 L 304 74 L 324.26666666666665 74 L 344.53333333333336 74 L 364.8 74 L 385.06666666666666 74 L 405.3333333333333 74 L 425.6 74 L 445.8666666666667 74 L 466.1333333333333 74 L 486.40000000000003 74 L 506.6666666666667 74 L 526.9333333333334 74 L 547.2 74 L 567.4666666666667 74 L 587.7333333333333 74 L 608 74 L 618 74" isTracker="true" stroke-linejoin="round" visibility="visible" stroke-opacity="0.0001" stroke="rgb(192,192,192)" stroke-width="420" style=""></path></g></g></svg></div></div>
</div>
<!-- /Chart -->






<!-- Dont display for deleted lists -->

<div class="box1">
 <h2 class="smaller">Campaigns</h2>
 
 <table class="listBox">
 <thead><tr>
  <th class="name">Name</th>
  <th class="expires">Expires</th>
  <th class="accessed">Accessed</th>
  <th class="optouts">Opt-Outs</th>
 </tr></thead>
 <tbody>
 
 <tr>
  <td><a href="campaign.html&amp;ia=d6ac2d6c5d03bc9a&amp;ic=aade7a2fca6986ad&amp;s=View">US - Ace Hardware Dewalt Heater - Revshare</a></td>
  <td>Never</td>
  <td>28 Times</td>
  <td class="optouts js_commify">293</td>
 </tr>
 
 <tr>
  <td><a href="campaign.html&amp;ia=d6ac2d6c5d03bc9a&amp;ic=b24adb6208a2a775&amp;s=View">US - Ace Hardware Pittsburgh Tool Set - Revshare</a></td>
  <td>Never</td>
  <td>10 Times</td>
  <td class="optouts js_commify">306</td>
 </tr>
 
 <tr>
  <td><a href="campaign.html&amp;ia=d6ac2d6c5d03bc9a&amp;ic=9d581fa5b59130d4&amp;s=View">US - Ace Hardware	Stanley Tool Set - Revshare</a></td>
  <td>Never</td>
  <td>22 Times</td>
  <td class="optouts js_commify">247</td>
 </tr>
 
 <tr>
  <td><a href="campaign.html&amp;ia=d6ac2d6c5d03bc9a&amp;ic=5715bb82b73de9e3&amp;s=View">US - Ace Hardware	Video Doorbell - Revshare</a></td>
  <td>Never</td>
  <td>14 Times</td>
  <td class="optouts js_commify">82</td>
 </tr>
 
 </tbody></table>
 <p class="boxButtons">
  <a href="campaign.html&amp;ia=d6ac2d6c5d03bc9a&amp;s=Create" class="button">Create a Campaign</a>
  <a href="campaign.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682" class="button buttonDull">Expand View</a>
 </p>
 
</div>




<!-- /deleted -->

<div class="box1">
 <h2 class="smaller">Transfers</h2>
 
 <p class="notice">Currently no Transfer have been created. <a href="transfer.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=Create">Create a Transfer now</a></p>
 
</div>

<div class="box1">
  <h2 class="smaller">Imports</h2>
  
  <p class="notice">Currently no Imports have been created.</p>
  
 </div>

<div class="box1">
 <h2 class="small">Opt-Out List Log</h2>
 
 <table class="listBox listLog"><tbody>
 
 <tr>
  <td class="ts">Oct 19, 2024 05:05</td>
  <td class="message">'spprthsi.xyz (ID 4523)' downloaded the suppression file (6.60 mb) for campaign 'US - Ace Hardware	Stanley Tool Set - Revshare (ID 17218)'</td>
 </tr>
 
 <tr>
  <td class="ts">Oct 19, 2024 05:05</td>
  <td class="message">'spprthsi.xyz (ID 4523)' prepared a download for campaign 'US - Ace Hardware	Stanley Tool Set - Revshare (ID 17218)' in MD5 (.zip) format, 6.60 mb</td>
 </tr>
 
 <tr>
  <td class="ts">Oct 18, 2024 18:08</td>
  <td class="message">Prepared Publisher access link and Opt-Out for 'Imade Marketing' in campaign 'US - Ace Hardware	Stanley Tool Set - Revshare' (via Everflow API)</td>
 </tr>
 
 <tr>
  <td class="ts">Oct 17, 2024 12:48</td>
  <td class="message">Prepared Publisher access link and Opt-Out for 'Xtier' in campaign 'US - Ace Hardware	Stanley Tool Set - Revshare' (via Everflow API)</td>
 </tr>
 
 <tr>
  <td class="ts">Oct 17, 2024 12:08</td>
  <td class="message">Prepared Publisher access link and Opt-Out for 'EXT' in campaign 'US - Ace Hardware	Stanley Tool Set - Revshare' (via Everflow API)</td>
 </tr>
 
 </tbody></table>
 <p class="boxButtons"><a href="log.html&amp;ia=d6ac2d6c5d03bc9a&amp;io=4628f1744a3c6682&amp;s=OptOut" class="button buttonDull">View All</a></p>
 
</div>

<script type="text/javascript">
window.addEvent('load', function() {
	

  


</script>

</div> <!-- /content -->

<div id="clearfooter"></div>
</div> <!-- /container -->


	<!-- Footer is provided by React. -->



<script type="text/javascript">
if (self == top) {
	var theBody = document.getElementsByTagName('body')[0];
	theBody.style.display = "block";
} else {
	top.location = self.location;
}
</script>


<script type="text/javascript" src="/include/core.js?v=1748490996"></script>
<script type="text/javascript" src="/include/langselect.js"></script>
<script type="text/javascript">new LangSelect();</script>




	<!-- Inject the react script -->
	  <script type="module" crossorigin="anonymous" src="https://suppress.optizmo.com/assets/index-PLBeLsC0.js"></script>






<iframe data-product="web_widget" title="No content" role="presentation" tabindex="-1" allow="microphone *" aria-hidden="true" src="about:blank" style="width: 0px; height: 0px; border: 0px; position: absolute; top: -9999px;"></iframe><div><iframe title="Opens a widget where you can find more information" id="launcher" tabindex="0" style="color-scheme: light; width: 0px; height: 50px; padding: 0px; margin: 10px 20px; position: fixed; bottom: 0px; overflow: visible; opacity: 1; border: 0px; z-index: 999998; transition-duration: 250ms; transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1); transition-property: opacity, top, bottom; right: 0px;"></iframe></div></body></html>