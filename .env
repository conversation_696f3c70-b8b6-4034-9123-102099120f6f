# Optizmo API Configuration
OPTIZMO_API_KEY=optizmo_api_7ngQN9b6Z1vr6Ka2nfFJx6RkZQpm312EQKLvDfuuDUl3lH_s0kJb3mATqEGnv0TQyTL9z53OAbz84uVxdD129K0O

# Optizmo Web Interface Credentials (for automatic list discovery)
OPTIZMO_WEB_USERNAME=Bizaglo
OPTIZMO_WEB_PASSWORD=Bizaz1234

# Optional: Override default URLs
# OPTIZMO_BASE_URL=https://api.optizmo.com
# OPTIZMO_COLLECT_URL=https://collect.optoutsystem.com
# OPTIZMO_WEB_BASE_URL=https://client.optizmo.net

# Optional: Adjust timeouts and limits
# OPTIZMO_TIMEOUT=30
# OPTIZMO_MAX_RETRIES=3
# OPTIZMO_RETRY_DELAY=1.0
# OPTIZMO_RATE_LIMIT_DELAY=0.1

# Optional: Browser configuration
OPTIZMO_HEADLESS_BROWSER=false
OPTIZMO_BROWSER_TIMEOUT=60

# Logging Configuration
# LOG_LEVEL=INFO
# LOG_FILE_PATH=optizmo_analyzer.log
