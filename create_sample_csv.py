#!/usr/bin/env python3
"""
Create a sample CSV file with the extracted Optizmo data for examination.
"""

import sys
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.analyzer import OptizmoAnalyzer
from optizmo_analyzer.cli import _save_to_csv


def create_sample_csv():
    """Create a sample CSV file with real extracted data."""
    print("📄 Creating Sample CSV with Extracted Optizmo Data")
    print("=" * 60)
    
    try:
        # Create configuration
        config = OptizmoConfig.from_env()
        print(f"✅ Configuration loaded")
        
        # Create analyzer
        analyzer = OptizmoAnalyzer(config)
        print(f"✅ Analyzer created")
        
        print(f"\n📋 Discovering and analyzing lists...")
        
        # Discover lists from web interface (limited to first 10 for sample)
        discovered_lists = analyzer.discover_list_ids_from_web()
        
        if not discovered_lists:
            print(f"❌ No lists discovered")
            return
        
        print(f"✅ Found {len(discovered_lists)} lists")
        
        # Analyze the first 10 lists
        print(f"📊 Analyzing first 10 lists...")
        analysis = analyzer.analyze_multiple_lists(discovered_lists[:10])
        
        print(f"✅ Analyzed {len(analysis.lists)} lists")
        
        # Create CSV file
        csv_filename = "optizmo_sample_data.csv"
        print(f"\n💾 Creating CSV file: {csv_filename}")
        
        _save_to_csv(analysis, csv_filename)
        
        print(f"✅ CSV file created successfully!")
        print(f"\n📋 CSV file details:")
        print(f"   📁 Filename: {csv_filename}")
        print(f"   📊 Records: {len(analysis.lists)}")
        
        # Show what fields are included
        print(f"\n📋 CSV Fields included:")
        fieldnames = ['ia', 'io', 'list_name', 'list_type', 'items_count', 'times_accessed', 'last_accessed', 'created_at', 'times_cleansed', 'downloaded_times', 'opt_out_list_id']
        for i, field in enumerate(fieldnames, 1):
            print(f"   {i:2d}. {field}")
        
        # Show sample of the data
        print(f"\n📋 Sample data (first 3 records):")
        for i, optout_list in enumerate(analysis.lists[:3], 1):
            print(f"\n   {i}. {optout_list.name[:40]}...")
            print(f"      IA: {optout_list.ia}")
            print(f"      IO: {optout_list.io}")
            print(f"      Created: {getattr(optout_list, 'created_at', 'Unknown')}")
            print(f"      Downloaded: {getattr(optout_list, 'downloaded_times', 'Unknown')}x")
            print(f"      Cleansed: {getattr(optout_list, 'times_cleansed', 'Unknown')}x")
            print(f"      OptOut ID: {getattr(optout_list, 'opt_out_list_id', 'Unknown')}")
        
        # Read and show first few lines of the CSV
        print(f"\n📄 CSV file preview (first 5 lines):")
        try:
            with open(csv_filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:5], 1):
                    print(f"   {i}: {line.strip()}")
                if len(lines) > 5:
                    print(f"   ... and {len(lines) - 5} more lines")
        except Exception as e:
            print(f"   ❌ Error reading CSV: {e}")
        
        print(f"\n🎉 SUCCESS! You can now open '{csv_filename}' in Excel or any CSV viewer")
        print(f"📍 File location: {Path.cwd() / csv_filename}")
                
    except Exception as e:
        print(f"❌ Failed to create CSV: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    create_sample_csv()
