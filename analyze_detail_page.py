#!/usr/bin/env python3
"""
Script to login to Optizmo and analyze the detail page HTML structure
to find where the fields "Created :", "Downloaded :", "Cleansed :", "Opt-Out List Id :" are located.
"""

import sys
from pathlib import Path
import re

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.web_scraper import OptizmoWebScraper


def analyze_detail_page():
    """Analyze the detail page HTML structure."""
    print("🔍 Analyzing Optizmo Detail Page Structure")
    print("=" * 60)
    
    try:
        # Create configuration
        config = OptizmoConfig.from_env()
        print(f"✅ Configuration loaded")
        
        # Create web scraper
        with OptizmoWebScraper(config) as scraper:
            print(f"\n🔐 Logging in...")
            scraper.login()
            print(f"✅ Login successful")
            
            # Navigate to the specific detail page
            detail_url = "https://client.optizmo.net/optouts.html&ia=d6ac2d6c5d03bc9a&io=4628f1744a3c6682&s=View"
            print(f"\n🌐 Navigating to detail page:")
            print(f"   {detail_url}")
            
            scraper.driver.get(detail_url)
            import time
            time.sleep(5)  # Wait for page to load
            
            print(f"\n📄 Page loaded. Title: {scraper.driver.title}")
            
            # Get the full page HTML
            page_html = scraper.driver.page_source
            print(f"\n📊 Page HTML length: {len(page_html)} characters")
            
            # Look for the fields we want in the page HTML
            target_fields = ["Created :", "Downloaded :", "Cleansed :", "Opt-Out List Id :"]
            
            print(f"\n🔍 Searching for target fields in page HTML...")
            for field in target_fields:
                if field in page_html:
                    print(f"   ✅ Found '{field}' in page HTML")
                else:
                    print(f"   ❌ '{field}' NOT found in page HTML")
            
            # Look for variations of the fields
            print(f"\n🔍 Searching for field variations...")
            field_variations = {
                "Created": ["Created", "created", "CREATED", "Created:", "created:"],
                "Downloaded": ["Downloaded", "downloaded", "DOWNLOADED", "Downloaded:", "downloaded:"],
                "Cleansed": ["Cleansed", "cleansed", "CLEANSED", "Cleansed:", "cleansed:"],
                "Opt-Out List Id": ["Opt-Out List Id", "opt-out list id", "OptOut List Id", "Opt Out List Id", "List Id"]
            }
            
            for field_name, variations in field_variations.items():
                found_variations = []
                for variation in variations:
                    if variation in page_html:
                        found_variations.append(variation)
                
                if found_variations:
                    print(f"   ✅ {field_name}: Found variations: {found_variations}")
                else:
                    print(f"   ❌ {field_name}: No variations found")
            
            # Look for div.box2.boxDetails specifically
            print(f"\n🔍 Looking for div.box2.boxDetails...")
            try:
                details_box = scraper.driver.find_element(By.CSS_SELECTOR, "div.box2.boxDetails")
                print(f"   ✅ Found div.box2.boxDetails")
                
                details_html = details_box.get_attribute('outerHTML')
                print(f"   📏 Details box HTML length: {len(details_html)} characters")
                
                # Save the details box HTML to a file for analysis
                with open('details_box.html', 'w', encoding='utf-8') as f:
                    f.write(details_html)
                print(f"   💾 Saved details box HTML to 'details_box.html'")
                
                # Show first part of details box HTML
                print(f"\n📋 Details box HTML (first 2000 characters):")
                print("=" * 60)
                print(details_html[:2000])
                print("=" * 60)
                
                # Look for our target fields in the details box
                print(f"\n🔍 Searching for target fields in details box...")
                details_text = details_box.text
                print(f"   📝 Details box text: {details_text}")
                
                for field in target_fields:
                    if field in details_html:
                        print(f"   ✅ Found '{field}' in details box HTML")
                        
                        # Find the context around this field
                        start_pos = details_html.find(field)
                        context_start = max(0, start_pos - 100)
                        context_end = min(len(details_html), start_pos + 200)
                        context = details_html[context_start:context_end]
                        print(f"      Context: ...{context}...")
                    else:
                        print(f"   ❌ '{field}' NOT found in details box HTML")
                
            except Exception as e:
                print(f"   ❌ Could not find div.box2.boxDetails: {e}")
                
                # Look for any div with "box" in the class
                print(f"\n🔍 Looking for any div with 'box' in class name...")
                try:
                    from selenium.webdriver.common.by import By
                    box_divs = scraper.driver.find_elements(By.CSS_SELECTOR, "div[class*='box']")
                    print(f"   📦 Found {len(box_divs)} div elements with 'box' in class")
                    
                    for i, box_div in enumerate(box_divs[:5]):  # Show first 5
                        box_class = box_div.get_attribute('class')
                        box_text = box_div.text[:100] if box_div.text else "(no text)"
                        print(f"      Box {i+1}: class='{box_class}', text='{box_text}...'")
                        
                        # Check if this box contains our target fields
                        box_html = box_div.get_attribute('outerHTML')
                        found_fields = []
                        for field in target_fields:
                            if field in box_html:
                                found_fields.append(field)
                        
                        if found_fields:
                            print(f"         🎯 This box contains: {found_fields}")
                            print(f"         📋 Box HTML (first 1000 chars): {box_html[:1000]}...")
                
                except Exception as e2:
                    print(f"   ❌ Error searching for box divs: {e2}")
            
            # Save the full page HTML for analysis
            with open('full_page.html', 'w', encoding='utf-8') as f:
                f.write(page_html)
            print(f"\n💾 Saved full page HTML to 'full_page.html'")
            
            print(f"\n✅ Analysis complete!")
            print(f"📁 Check the saved HTML files for detailed structure analysis")
                
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    analyze_detail_page()
