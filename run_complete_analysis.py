#!/usr/bin/env python3
"""
Complete Optizmo Analysis - Process ALL lists from ALL pages
"""

import sys
from pathlib import Path
from datetime import datetime

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.analyzer import OptizmoAnalyzer
from optizmo_analyzer.cli import _save_to_csv


def run_complete_analysis():
    """Run the complete analysis on ALL lists from ALL pages."""
    print("🚀 COMPLETE OPTIZMO ANALYSIS - ALL LISTS, ALL PAGES")
    print("=" * 70)
    
    start_time = datetime.now()
    print(f"⏰ Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Create configuration
        config = OptizmoConfig.from_env()
        print(f"✅ Configuration loaded")
        print(f"   Web base URL: {config.web_base_url}")
        print(f"   Username: {config.web_username}")
        print(f"   Headless mode: {config.headless_browser}")
        
        # Create analyzer
        analyzer = OptizmoAnalyzer(config)
        print(f"✅ Analyzer created")
        
        print(f"\n🔍 PHASE 1: DISCOVERING ALL LISTS FROM ALL PAGES")
        print("=" * 50)
        
        # Discover ALL lists from ALL pages
        print(f"📋 Starting comprehensive list discovery...")
        discovered_lists = analyzer.discover_list_ids_from_web()
        
        if not discovered_lists:
            print(f"❌ No lists discovered")
            return
        
        total_lists = len(discovered_lists)
        print(f"\n✅ PHASE 1 COMPLETE!")
        print(f"   📊 Total lists discovered: {total_lists}")
        
        # Show summary by list type
        regular_lists = sum(1 for lst in discovered_lists if lst.get('type') == 'Regular List')
        linked_lists = sum(1 for lst in discovered_lists if lst.get('type') == 'Linked List')
        unknown_type = total_lists - regular_lists - linked_lists
        
        print(f"   📋 List breakdown:")
        print(f"      • Regular Lists: {regular_lists}")
        print(f"      • Linked Lists: {linked_lists}")
        if unknown_type > 0:
            print(f"      • Unknown Type: {unknown_type}")
        
        # Calculate estimated time
        estimated_seconds = total_lists * 3  # Roughly 3 seconds per list for detail extraction
        estimated_minutes = estimated_seconds / 60
        print(f"   ⏱️  Estimated time for Phase 2: ~{estimated_minutes:.1f} minutes")
        
        print(f"\n🔍 PHASE 2: EXTRACTING DETAILS FOR ALL {total_lists} LISTS")
        print("=" * 50)
        
        # Analyze ALL discovered lists
        print(f"📊 Starting comprehensive analysis...")
        analysis = analyzer.analyze_multiple_lists(discovered_lists)
        
        analyzed_count = len(analysis.lists)
        print(f"\n✅ PHASE 2 COMPLETE!")
        print(f"   📊 Lists analyzed: {analyzed_count}/{total_lists}")
        
        # Check detail extraction success rate
        successful_extractions = 0
        for optout_list in analysis.lists:
            extracted_fields = 0
            for field in ['created_at', 'times_cleansed', 'downloaded_times', 'opt_out_list_id']:
                value = getattr(optout_list, field, 'Unknown')
                if value and value != 'Unknown':
                    extracted_fields += 1
            if extracted_fields > 0:
                successful_extractions += 1
        
        success_rate = (successful_extractions / analyzed_count) * 100 if analyzed_count > 0 else 0
        print(f"   📈 Detail extraction success rate: {success_rate:.1f}% ({successful_extractions}/{analyzed_count})")
        
        print(f"\n💾 PHASE 3: GENERATING COMPLETE CSV EXPORT")
        print("=" * 50)
        
        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"optizmo_complete_analysis_{timestamp}.csv"
        
        print(f"📄 Creating comprehensive CSV: {csv_filename}")
        _save_to_csv(analysis, csv_filename)
        
        print(f"✅ CSV export complete!")
        
        # Final summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 COMPLETE ANALYSIS FINISHED!")
        print("=" * 50)
        print(f"⏰ Started:  {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ Finished: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  Duration: {duration}")
        print(f"📊 Total lists processed: {analyzed_count}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        print(f"📁 CSV file: {csv_filename}")
        print(f"📍 Location: {Path.cwd() / csv_filename}")
        
        # Show sample of results
        print(f"\n📋 SAMPLE RESULTS (first 5 lists):")
        for i, optout_list in enumerate(analysis.lists[:5], 1):
            print(f"\n   {i}. {optout_list.name[:50]}...")
            print(f"      IA: {optout_list.ia}")
            print(f"      IO: {optout_list.io}")
            print(f"      Type: {getattr(optout_list, 'list_type', 'Unknown')}")
            print(f"      Records: {optout_list.record_count:,}")
            print(f"      Created: {getattr(optout_list, 'created_at', 'Unknown')}")
            print(f"      Downloaded: {getattr(optout_list, 'downloaded_times', 'Unknown')}x")
            print(f"      Cleansed: {getattr(optout_list, 'times_cleansed', 'Unknown')}x")
            print(f"      OptOut ID: {getattr(optout_list, 'opt_out_list_id', 'Unknown')}")
        
        if len(analysis.lists) > 5:
            print(f"\n   ... and {len(analysis.lists) - 5} more lists in the CSV file")
        
        print(f"\n🚀 SUCCESS! Complete analysis finished.")
        print(f"📊 Open '{csv_filename}' in Excel to view all results!")
                
    except Exception as e:
        print(f"❌ Complete analysis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_complete_analysis()
