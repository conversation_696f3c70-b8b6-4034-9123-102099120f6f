#!/usr/bin/env python3
"""
Test script to verify the detail extraction fixes for Optizmo web scraper.
"""

import sys
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.web_scraper import OptizmoWebScraper


def test_detail_extraction():
    """Test the detail extraction functionality."""
    print("🧪 Testing Optizmo Detail Extraction")
    print("=" * 50)
    
    try:
        # Create configuration
        config = OptizmoConfig.from_env()
        print(f"✅ Configuration loaded")
        print(f"   Web base URL: {config.web_base_url}")
        print(f"   Username: {config.web_username}")
        print(f"   Headless: {config.headless_browser}")
        
        # Create web scraper
        with OptizmoWebScraper(config) as scraper:
            print(f"\n🔐 Logging in...")
            scraper.login()
            print(f"✅ Login successful")
            
            # Test with a small number of lists
            print(f"\n🔍 Discovering lists (limited to 1 page for testing)...")
            discovered_lists = scraper.navigate_and_discover_with_pagination()
            
            if discovered_lists:
                print(f"\n📊 Discovery Results:")
                print(f"   Total lists found: {len(discovered_lists)}")
                
                # Show sample of results
                print(f"\n📋 Sample results (first 3 lists):")
                for i, list_info in enumerate(discovered_lists[:3], 1):
                    print(f"\n   {i}. {list_info['name'][:50]}...")
                    print(f"      IA: {list_info['ia']}")
                    print(f"      IO: {list_info['io']}")
                    print(f"      Type: {list_info['type']}")
                    print(f"      Records: {list_info['record_count']}")
                    print(f"      Times Accessed: {list_info['times_accessed']}")
                    print(f"      Last Accessed: {list_info['last_accessed']}")
                    print(f"      Created At: {list_info['created_at']}")
                    print(f"      Times Cleansed: {list_info['times_cleansed']}")
                    print(f"      Downloaded Times: {list_info['downloaded_times']}")
                    print(f"      Opt-Out List ID: {list_info['opt_out_list_id']}")
                
                # Check if detail extraction worked
                detail_fields = ['created_at', 'times_cleansed', 'downloaded_times', 'opt_out_list_id']
                successful_extractions = 0
                
                for list_info in discovered_lists[:10]:  # Check first 10 lists
                    extracted_fields = 0
                    for field in detail_fields:
                        if list_info.get(field, 'Unknown') != 'Unknown':
                            extracted_fields += 1
                    
                    if extracted_fields > 0:
                        successful_extractions += 1
                
                print(f"\n📈 Detail Extraction Summary:")
                print(f"   Lists checked: {min(10, len(discovered_lists))}")
                print(f"   Lists with extracted details: {successful_extractions}")
                print(f"   Success rate: {(successful_extractions / min(10, len(discovered_lists))) * 100:.1f}%")
                
                if successful_extractions > 0:
                    print(f"   ✅ Detail extraction is working!")
                else:
                    print(f"   ❌ Detail extraction failed - all fields remain 'Unknown'")
                    print(f"   🔍 Check the debug output above for clues")
                
            else:
                print(f"❌ No lists discovered")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_detail_extraction()
