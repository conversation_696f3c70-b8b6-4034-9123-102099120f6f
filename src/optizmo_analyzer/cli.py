"""
Command-line interface for Optizmo Lists Analyzer.
"""

import csv
import logging
import sys
from typing import List, Dict
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
import json

from .config import OptizmoConfig, LoggingConfig
from .analyzer import OptizmoAnalyzer
from .models import ListAnalysis


console = Console()


def setup_logging(config: LoggingConfig) -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, config.level.upper()),
        format=config.format,
        filename=config.file_path
    )


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--log-file', help='Path to log file')
@click.pass_context
def cli(ctx: click.Context, verbose: bool, log_file: str) -> None:
    """Optizmo Lists Analyzer - Analyze optout lists for cost optimization."""
    ctx.ensure_object(dict)

    # Setup logging
    log_config = LoggingConfig.from_env()
    if verbose:
        log_config.level = "DEBUG"
    if log_file:
        log_config.file_path = log_file

    setup_logging(log_config)

    # Store config in context
    try:
        ctx.obj['config'] = OptizmoConfig.from_env()
    except ValueError as e:
        console.print(f"[red]Configuration error: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--list-id', required=True, help='Optout list ID to analyze')
@click.option('--list-name', help='Optional name for the list')
@click.option('--output', '-o', help='Output file path (JSON format)')
@click.pass_context
def analyze_single(ctx: click.Context, list_id: str, list_name: str, output: str) -> None:
    """Analyze a single optout list."""
    config = ctx.obj['config']
    analyzer = OptizmoAnalyzer(config)

    console.print(f"[blue]Analyzing list: {list_id}[/blue]")

    try:
        with console.status("[bold green]Analyzing list..."):
            optout_list = analyzer.analyze_list(list_id, list_name)

        # Display results
        _display_single_list_results(optout_list)

        # Save to file if requested
        if output:
            with open(output, 'w') as f:
                json.dump(optout_list.model_dump(), f, indent=2, default=str)
            console.print(f"[green]Results saved to {output}[/green]")

    except Exception as e:
        console.print(f"[red]Error analyzing list: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--lists-file', required=True, help='JSON file containing list configurations')
@click.option('--output', '-o', help='Output file path (JSON format)')
@click.pass_context
def analyze_multiple(ctx: click.Context, lists_file: str, output: str) -> None:
    """Analyze multiple optout lists from a configuration file."""
    config = ctx.obj['config']
    analyzer = OptizmoAnalyzer(config)

    # Load list configurations
    try:
        with open(lists_file, 'r') as f:
            list_configs = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        console.print(f"[red]Error loading lists file: {e}[/red]")
        sys.exit(1)

    console.print(f"[blue]Analyzing {len(list_configs)} lists[/blue]")

    try:
        with console.status("[bold green]Analyzing lists..."):
            analysis = analyzer.analyze_multiple_lists(list_configs)

        # Display results
        _display_analysis_results(analysis)

        # Generate recommendations
        recommendations = analyzer.get_cost_optimization_recommendations(analysis)
        _display_recommendations(recommendations)

        # Save to file if requested
        if output:
            output_data = {
                "analysis": analysis.model_dump(),
                "recommendations": recommendations
            }
            with open(output, 'w') as f:
                json.dump(output_data, f, indent=2, default=str)
            console.print(f"[green]Results saved to {output}[/green]")

    except Exception as e:
        console.print(f"[red]Error analyzing lists: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--output', '-o', help='Output file path (JSON format)')
@click.option('--csv', help='CSV output file path')
@click.option('--method', type=click.Choice(['web', 'activity', 'auto']), default='auto',
              help='Discovery method: web (scrape web interface), activity (download activity), auto (try both)')
@click.pass_context
def discover_and_analyze(ctx: click.Context, output: str, csv: str, method: str) -> None:
    """Discover all optout lists automatically and analyze them."""
    config = ctx.obj['config']
    analyzer = OptizmoAnalyzer(config)

    list_configs = []

    if method in ['web', 'auto']:
        console.print("[blue]Discovering optout lists from web interface...[/blue]")
        try:
            with console.status("[bold green]Logging in and discovering lists..."):
                list_configs = analyzer.discover_list_ids_from_web()

            if list_configs:
                console.print(f"[green]Discovered {len(list_configs)} lists from web interface![/green]")
            elif method == 'web':
                console.print("[yellow]No lists found via web scraping.[/yellow]")
                return

        except Exception as e:
            console.print(f"[red]Web discovery failed: {e}[/red]")
            if method == 'web':
                sys.exit(1)
            console.print("[yellow]Falling back to download activity method...[/yellow]")

    if not list_configs and method in ['activity', 'auto']:
        console.print("[blue]Discovering optout lists from download activity...[/blue]")
        try:
            with console.status("[bold green]Discovering lists..."):
                list_configs = analyzer.discover_list_ids_from_activity()

            if list_configs:
                console.print(f"[green]Discovered {len(list_configs)} lists from download activity![/green]")

        except Exception as e:
            console.print(f"[red]Activity discovery failed: {e}[/red]")

    if not list_configs:
        console.print("[yellow]No lists found with any discovery method. This might mean:[/yellow]")
        console.print("  • No lists have been downloaded recently (last 90 days)")
        console.print("  • Web credentials are incorrect or missing")
        console.print("  • You may need to use the manual approach with specific list IDs")
        console.print("  • Try running 'create-sample-config' to create a manual configuration")
        return

    # Show discovered lists
    table = Table(title="Discovered Lists")
    table.add_column("IA", style="cyan")
    table.add_column("IO", style="magenta")
    table.add_column("Name", style="white")
    table.add_column("Type", style="green")
    table.add_column("Records", style="yellow")
    table.add_column("Created", style="blue")
    table.add_column("Cleansed", style="red")
    table.add_column("Downloaded", style="orange1")

    for lst in list_configs:
        table.add_row(
            lst['ia'][:8] + '...',
            lst['io'][:8] + '...',
            lst['name'][:30] + ('...' if len(lst['name']) > 30 else ''),
            lst.get('type', 'Unknown'),
            str(lst.get('record_count', 'Unknown')),
            lst.get('created_at', 'Unknown'),
            lst.get('times_cleansed', 'Unknown') + 'x' if lst.get('times_cleansed', 'Unknown') != 'Unknown' else 'Unknown',
            lst.get('downloaded_times', 'Unknown') + 'x' if lst.get('downloaded_times', 'Unknown') != 'Unknown' else 'Unknown'
        )

    console.print(table)

    # Analyze all discovered lists
    console.print(f"\n[blue]Analyzing {len(list_configs)} lists...[/blue]")

    with console.status("[bold green]Analyzing lists..."):
        analysis = analyzer.analyze_multiple_lists(list_configs)

    # Display results
    _display_analysis_results(analysis)

    # Generate recommendations
    recommendations = analyzer.get_cost_optimization_recommendations(analysis)
    _display_recommendations(recommendations)

    # Save to file if requested
    if output:
        output_data = {
            "analysis": analysis.model_dump(),
            "recommendations": recommendations
        }
        with open(output, 'w') as f:
            json.dump(output_data, f, indent=2, default=str)
        console.print(f"[green]Results saved to {output}[/green]")

    # Save to CSV if requested
    if csv:
        _save_to_csv(analysis, csv)
        console.print(f"[green]Results saved to CSV: {csv}[/green]")


@cli.command()
@click.pass_context
def create_sample_config(ctx: click.Context) -> None:
    """Create a sample lists configuration file."""
    sample_config = [
        {"id": "901fad4c46a1e723", "name": "Main Email List"},
        {"id": "abc123def456", "name": "SMS Optouts"},
        {"id": "xyz789uvw012", "name": "Legacy List"}
    ]

    with open("sample_lists.json", 'w') as f:
        json.dump(sample_config, f, indent=2)

    console.print("[green]Sample configuration created: sample_lists.json[/green]")
    console.print("Edit this file with your actual list IDs and names.")


def _display_single_list_results(optout_list) -> None:
    """Display results for a single list analysis."""
    table = Table(title="List Analysis Results")
    table.add_column("Property", style="cyan")
    table.add_column("Value", style="green")

    table.add_row("List ID", optout_list.id)
    table.add_row("Name", optout_list.name)
    table.add_row("Record Count", str(optout_list.record_count))
    table.add_row("Created Date", optout_list.created_date.strftime("%Y-%m-%d %H:%M:%S"))
    table.add_row("Status", optout_list.status or "Unknown")

    if optout_list.description:
        table.add_row("Description", optout_list.description)

    console.print(table)


def _display_analysis_results(analysis: ListAnalysis) -> None:
    """Display results for multiple lists analysis."""
    # Summary panel
    summary_text = Text()
    summary_text.append(f"Total Lists: {analysis.total_lists}\n", style="bold")
    summary_text.append(f"Total Records: {analysis.total_records:,}\n", style="bold")
    summary_text.append(f"Analysis Date: {analysis.analysis_date.strftime('%Y-%m-%d %H:%M:%S')}", style="dim")

    console.print(Panel(summary_text, title="Analysis Summary", border_style="blue"))

    # Lists table
    table = Table(title="Optout Lists (Sorted by Creation Date)")
    table.add_column("IA/IO", style="cyan")
    table.add_column("Name", style="white")
    table.add_column("Records", justify="right", style="green")
    table.add_column("Created", style="yellow")
    table.add_column("Last Access", style="blue")
    table.add_column("Cleansed", style="red")
    table.add_column("Downloaded", style="orange1")
    table.add_column("OptOut ID", style="magenta")

    for lst in analysis.lists:
        # Handle None/Unknown values with proper defaults
        created_at = getattr(lst, 'created_at', 'Never')
        if not created_at or created_at == 'Unknown':
            created_at = 'Never'

        last_accessed = getattr(lst, 'last_accessed', 'Never')
        if not last_accessed or last_accessed == 'Unknown':
            last_accessed = 'Never'

        times_cleansed = getattr(lst, 'times_cleansed', '0')
        if not times_cleansed or times_cleansed == 'Unknown':
            times_cleansed = '0'

        downloaded_times = getattr(lst, 'downloaded_times', '0')
        if not downloaded_times or downloaded_times == 'Unknown':
            downloaded_times = '0'

        opt_out_list_id = getattr(lst, 'opt_out_list_id', 'Unknown')
        if not opt_out_list_id:
            opt_out_list_id = 'Unknown'

        table.add_row(
            f"{lst.ia[:8]}.../{lst.io[:8]}...",
            lst.name[:40] + ('...' if len(lst.name) > 40 else ''),
            f"{lst.record_count:,}",
            created_at,
            last_accessed,
            times_cleansed + 'x' if times_cleansed != '0' else '0',
            downloaded_times + 'x' if downloaded_times != '0' else '0',
            opt_out_list_id[:12] + '...' if len(opt_out_list_id) > 12 else opt_out_list_id
        )

    console.print(table)


def _display_recommendations(recommendations: List[Dict]) -> None:
    """Display cost optimization recommendations."""
    if not recommendations:
        console.print("[yellow]No specific recommendations generated.[/yellow]")
        return

    console.print("\n")
    console.print(Panel("Cost Optimization Recommendations", border_style="green"))

    for i, rec in enumerate(recommendations, 1):
        priority_color = {
            "high": "red",
            "medium": "yellow",
            "low": "blue"
        }.get(rec["priority"], "white")

        rec_text = Text()
        rec_text.append(f"{i}. ", style="bold")
        rec_text.append(f"[{rec['priority'].upper()}] ", style=f"bold {priority_color}")
        rec_text.append(f"{rec['description']}\n", style="white")
        rec_text.append(f"   Lists affected: {len(rec['lists'])}\n", style="dim")
        rec_text.append(f"   Potential savings: {rec['potential_savings']}", style="dim")

        console.print(rec_text)


def _save_to_csv(analysis, csv_path: str) -> None:
    """Save analysis results to CSV file.

    Args:
        analysis: Analysis results object
        csv_path: Path to save CSV file
    """
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['ia', 'io', 'list_name', 'list_type', 'items_count', 'times_accessed', 'last_accessed', 'created_at', 'times_cleansed', 'downloaded_times', 'opt_out_list_id']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # Write header
        writer.writeheader()

        # Write data rows
        for optout_list in analysis.lists:
            # Determine list type based on available information
            list_type = getattr(optout_list, 'list_type', 'Regular List')
            if not list_type or list_type == 'Unknown':
                list_type = 'Regular List'

            # Handle None/Unknown values with proper defaults
            times_accessed = getattr(optout_list, 'times_accessed', 'Unknown')
            if not times_accessed or times_accessed == 'Unknown':
                times_accessed = 'Unknown'

            last_accessed = getattr(optout_list, 'last_accessed', 'Never')
            if not last_accessed or last_accessed == 'Unknown':
                last_accessed = 'Never'

            created_at = getattr(optout_list, 'created_at', 'Never')
            if not created_at or created_at == 'Unknown':
                created_at = 'Never'

            times_cleansed = getattr(optout_list, 'times_cleansed', '0')
            if not times_cleansed or times_cleansed == 'Unknown':
                times_cleansed = '0'

            downloaded_times = getattr(optout_list, 'downloaded_times', '0')
            if not downloaded_times or downloaded_times == 'Unknown':
                downloaded_times = '0'

            opt_out_list_id = getattr(optout_list, 'opt_out_list_id', 'Unknown')
            if not opt_out_list_id:
                opt_out_list_id = 'Unknown'

            writer.writerow({
                'ia': optout_list.ia,
                'io': optout_list.io,
                'list_name': optout_list.name,
                'list_type': list_type,
                'items_count': optout_list.record_count,
                'times_accessed': times_accessed,
                'last_accessed': last_accessed,
                'created_at': created_at,
                'times_cleansed': times_cleansed,
                'downloaded_times': downloaded_times,
                'opt_out_list_id': opt_out_list_id
            })


def main() -> None:
    """Main entry point for the CLI."""
    cli()


if __name__ == "__main__":
    main()
