"""
Main analyzer logic for Optizmo Lists Analyzer.
"""

import logging
import time
from datetime import datetime
from typing import List, Dict, Optional
import zipfile
import csv
import io
import requests

from .client import OptizmoClient, OptizmoAPIError
from .models import OptoutList, ListAnalysis
from .config import OptizmoConfig
from .web_scraper import OptizmoWebScraper, OptizmoWebScraperError


logger = logging.getLogger(__name__)


class OptizmoAnalyzer:
    """Main analyzer class for Optizmo optout lists."""

    def __init__(self, config: OptizmoConfig):
        """Initialize the analyzer.

        Args:
            config: Configuration object
        """
        self.config = config
        self.client = OptizmoClient(config)

    def discover_list_ids_from_activity(self) -> List[Dict[str, str]]:
        """Discover list IDs from download activity export.

        Returns:
            List of dictionaries with 'id' and 'name' keys

        Raises:
            OptizmoAPIError: If discovery fails
        """
        logger.info("Discovering list IDs from download activity")

        try:
            # Get download activity export
            export_url = self.client.get_download_activity_export()

            # Download and parse the export
            response = requests.get(export_url, timeout=self.config.timeout)
            response.raise_for_status()

            # Parse CSV content to extract list IDs
            list_ids = set()
            lines = response.text.strip().split('\n')

            if len(lines) > 1:  # Skip header
                reader = csv.reader(lines)
                header = next(reader, [])

                # Look for list ID column (common names)
                list_id_col = None
                list_name_col = None

                for i, col in enumerate(header):
                    col_lower = col.lower()
                    if 'list' in col_lower and 'id' in col_lower:
                        list_id_col = i
                    elif 'list' in col_lower and 'name' in col_lower:
                        list_name_col = i

                # Extract unique list IDs
                for row in reader:
                    if list_id_col is not None and len(row) > list_id_col:
                        list_id = row[list_id_col].strip()
                        if list_id:
                            list_name = row[list_name_col].strip() if list_name_col is not None and len(row) > list_name_col else f"List {list_id}"
                            list_ids.add((list_id, list_name))

            # Convert to list of dictionaries
            discovered_lists = [
                {"id": list_id, "name": list_name}
                for list_id, list_name in sorted(list_ids)
            ]

            logger.info(f"Discovered {len(discovered_lists)} unique list IDs")
            return discovered_lists

        except Exception as e:
            logger.error(f"Failed to discover list IDs: {e}")
            raise OptizmoAPIError(f"Failed to discover list IDs: {e}")

    def discover_list_ids_from_web(self) -> List[Dict[str, str]]:
        """Discover list IDs by scraping the Optizmo web interface.

        Returns:
            List of dictionaries with 'id' and 'name' keys

        Raises:
            OptizmoAPIError: If discovery fails
        """
        logger.info("Discovering list IDs from web interface")

        try:
            with OptizmoWebScraper(self.config) as scraper:
                # Login to Optizmo
                scraper.login()

                # Discover lists with pagination support
                discovered_lists = scraper.navigate_and_discover_with_pagination()

                logger.info(f"Discovered {len(discovered_lists)} lists from web interface")
                return discovered_lists

        except OptizmoWebScraperError as e:
            logger.error(f"Web scraping failed: {e}")
            raise OptizmoAPIError(f"Web scraping failed: {e}")
        except Exception as e:
            logger.error(f"Failed to discover list IDs from web: {e}")
            raise OptizmoAPIError(f"Failed to discover list IDs from web: {e}")

    def analyze_list(self, ia: str, io: str, list_name: str = None, list_type: str = None, record_count: str = None, times_accessed: str = None, last_accessed: str = None, created_at: str = None, times_cleansed: str = None, downloaded_times: str = None, opt_out_list_id: str = None) -> OptoutList:
        """Analyze a single optout list.

        Args:
            ia: The account/instance identifier (ia parameter)
            io: The object/list instance identifier (io parameter)
            list_name: Optional name for the list
            list_type: Optional type of the list (from web scraping)
            record_count: Optional record count (from web scraping)
            times_accessed: Optional number of times accessed (from web scraping)
            last_accessed: Optional last access date (from web scraping)
            created_at: Optional creation date (from web scraping)
            times_cleansed: Optional number of times cleansed (from web scraping)
            downloaded_times: Optional number of times downloaded (from web scraping)
            opt_out_list_id: Optional Optizmo internal list ID (from web scraping)

        Returns:
            OptoutList object with analysis data

        Raises:
            OptizmoAPIError: If analysis fails
        """
        logger.info(f"Analyzing list IA={ia}, IO={io}")

        try:
            # If we have record count from web scraping, use it to avoid API calls
            if record_count and record_count != "Unknown":
                try:
                    parsed_count = int(record_count.replace(',', '').replace(' ', ''))
                    # Parse created_at if available
                    created_date = None
                    if created_at and created_at != "Unknown":
                        try:
                            created_date = datetime.strptime(created_at, "%Y-%m-%d")
                        except ValueError:
                            created_date = datetime.now()
                    else:
                        created_date = datetime.now()

                    return OptoutList(
                        ia=ia,
                        io=io,
                        name=list_name or f"List {io}",
                        record_count=parsed_count,
                        created_date=created_date,
                        last_modified=datetime.now(),
                        list_type=list_type,
                        times_accessed=times_accessed,
                        last_accessed=last_accessed,
                        created_at=created_at,
                        times_cleansed=times_cleansed,
                        downloaded_times=downloaded_times,
                        opt_out_list_id=opt_out_list_id,
                        status="active"
                    )
                except (ValueError, TypeError):
                    # If parsing fails, fall back to API method
                    pass

            # Prepare download to get list information
            download_url = self.client.prepare_list_download(
                optout_list_id=io,  # Use io parameter as the specific list identifier
                columns=["plain", "timestamp"],
                collection_type=["collected", "imported"]
            )

            # Wait for download to be ready
            max_wait_time = 600  # 10 minutes
            wait_interval = 10   # 10 seconds
            elapsed_time = 0

            logger.info(f"Waiting for download to be ready for list IA={ia}, IO={io}")
            while elapsed_time < max_wait_time:
                if self.client.check_download_status(download_url):
                    break
                time.sleep(wait_interval)
                elapsed_time += wait_interval
                logger.debug(f"Still waiting... ({elapsed_time}s elapsed)")
            else:
                raise OptizmoAPIError(f"Download not ready after {max_wait_time} seconds")

            # Download and analyze the file
            api_record_count, created_date = self._analyze_download_file(download_url)

            return OptoutList(
                ia=ia,
                io=io,
                name=list_name or f"List {io}",
                record_count=api_record_count,
                created_date=created_date,
                last_modified=datetime.now(),
                list_type=list_type,
                times_accessed=times_accessed,
                last_accessed=last_accessed,
                created_at=created_at,
                times_cleansed=times_cleansed,
                downloaded_times=downloaded_times,
                opt_out_list_id=opt_out_list_id,
                status="active"
            )

        except OptizmoAPIError as e:
            logger.error(f"Failed to analyze list IA={ia}, IO={io}: {e}")
            # Return a basic list object with error information
            return OptoutList(
                ia=ia,
                io=io,
                name=list_name or f"List {io}",
                record_count=0,
                created_date=datetime.now(),
                description=f"Analysis failed: {e}",
                times_accessed=times_accessed,
                last_accessed=last_accessed,
                created_at=created_at,
                times_cleansed=times_cleansed,
                downloaded_times=downloaded_times,
                opt_out_list_id=opt_out_list_id,
                status="error"
            )

    def _analyze_download_file(self, download_url: str) -> tuple[int, datetime]:
        """Analyze the downloaded file to extract record count and creation date.

        Args:
            download_url: URL of the file to download

        Returns:
            Tuple of (record_count, earliest_date)
        """
        logger.info("Downloading and analyzing file")

        try:
            response = requests.get(download_url, timeout=self.config.timeout)
            response.raise_for_status()

            # Handle ZIP files
            if download_url.endswith('.zip'):
                return self._analyze_zip_file(response.content)
            else:
                return self._analyze_csv_content(response.text)

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to download file: {e}")
            raise OptizmoAPIError(f"Failed to download file: {e}")

    def _analyze_zip_file(self, zip_content: bytes) -> tuple[int, datetime]:
        """Analyze a ZIP file containing CSV data.

        Args:
            zip_content: ZIP file content as bytes

        Returns:
            Tuple of (record_count, earliest_date)
        """
        record_count = 0
        earliest_date = datetime.now()

        with zipfile.ZipFile(io.BytesIO(zip_content), 'r') as zip_file:
            for file_name in zip_file.namelist():
                if file_name.endswith('.csv'):
                    with zip_file.open(file_name) as csv_file:
                        csv_content = csv_file.read().decode('utf-8')
                        count, date = self._analyze_csv_content(csv_content)
                        record_count += count
                        if date < earliest_date:
                            earliest_date = date

        return record_count, earliest_date

    def _analyze_csv_content(self, csv_content: str) -> tuple[int, datetime]:
        """Analyze CSV content to extract record count and dates.

        Args:
            csv_content: CSV content as string

        Returns:
            Tuple of (record_count, earliest_date)
        """
        lines = csv_content.strip().split('\n')
        if not lines:
            return 0, datetime.now()

        # Skip header if present and handle empty content
        if not lines or (len(lines) == 1 and not lines[0].strip()):
            return 0, datetime.now()

        data_lines = lines[1:] if lines[0].lower().startswith(('email', 'plain', 'hash')) else lines
        record_count = len([line for line in data_lines if line.strip()])

        # Try to extract dates from timestamp column
        earliest_date = datetime.now()

        try:
            reader = csv.reader(lines)
            header = next(reader, [])

            # Find timestamp column
            timestamp_col = None
            for i, col in enumerate(header):
                if 'timestamp' in col.lower() or 'date' in col.lower():
                    timestamp_col = i
                    break

            if timestamp_col is not None:
                for row in reader:
                    if len(row) > timestamp_col and row[timestamp_col]:
                        try:
                            # Try different date formats
                            date_str = row[timestamp_col]
                            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y']:
                                try:
                                    date = datetime.strptime(date_str, fmt)
                                    if date < earliest_date:
                                        earliest_date = date
                                    break
                                except ValueError:
                                    continue
                        except (ValueError, IndexError):
                            continue

        except Exception as e:
            logger.warning(f"Could not parse dates from CSV: {e}")

        return record_count, earliest_date

    def analyze_multiple_lists(self, list_configs: List[Dict[str, str]]) -> ListAnalysis:
        """Analyze multiple optout lists.

        Args:
            list_configs: List of dictionaries with 'id' and optional 'name' keys

        Returns:
            ListAnalysis object with all analyzed lists
        """
        logger.info(f"Analyzing {len(list_configs)} lists")

        analyzed_lists = []
        total_records = 0

        for config in list_configs:
            ia = config['ia']
            io = config['io']
            list_name = config.get('name')
            list_type = config.get('type')
            record_count = config.get('record_count')
            times_accessed = config.get('times_accessed')
            last_accessed = config.get('last_accessed')
            created_at = config.get('created_at')
            times_cleansed = config.get('times_cleansed')
            downloaded_times = config.get('downloaded_times')
            opt_out_list_id = config.get('opt_out_list_id')

            # Debug logging to see what data we're getting
            print(f"🔍 DEBUG: Processing list '{list_name[:30] if list_name else 'Unknown'}...'")
            print(f"   📊 IA: {ia}, IO: {io}")
            print(f"   📅 created_at: '{created_at}'")
            print(f"   🧹 times_cleansed: '{times_cleansed}'")
            print(f"   📥 downloaded_times: '{downloaded_times}'")
            print(f"   🆔 opt_out_list_id: '{opt_out_list_id}'")

            try:
                optout_list = self.analyze_list(ia, io, list_name, list_type, record_count, times_accessed, last_accessed, created_at, times_cleansed, downloaded_times, opt_out_list_id)
                analyzed_lists.append(optout_list)
                total_records += optout_list.record_count

                created_str = optout_list.created_date.strftime('%Y-%m-%d') if optout_list.created_date else 'Unknown'
                logger.info(
                    f"Analyzed {optout_list.name}: {optout_list.record_count} records, "
                    f"created {created_str}, accessed {times_accessed}x"
                )

            except Exception as e:
                logger.error(f"Failed to analyze list IA={ia}, IO={io}: {e}")
                # Add error entry
                error_list = OptoutList(
                    ia=ia,
                    io=io,
                    name=list_name or f"List {io}",
                    record_count=0,
                    created_date=datetime.now(),
                    description=f"Analysis failed: {e}",
                    times_accessed=times_accessed,
                    last_accessed=last_accessed,
                    created_at=created_at,
                    times_cleansed=times_cleansed,
                    downloaded_times=downloaded_times,
                    opt_out_list_id=opt_out_list_id,
                    status="error"
                )
                analyzed_lists.append(error_list)

        # Sort by creation date (descending - newest first)
        analyzed_lists.sort(key=lambda x: x.created_date, reverse=True)

        return ListAnalysis(
            total_lists=len(analyzed_lists),
            total_records=total_records,
            lists=analyzed_lists,
            analysis_date=datetime.now()
        )

    def get_cost_optimization_recommendations(self, analysis: ListAnalysis) -> List[Dict[str, any]]:
        """Generate cost optimization recommendations based on analysis.

        Args:
            analysis: ListAnalysis object

        Returns:
            List of recommendation dictionaries
        """
        recommendations = []

        # Find lists with zero records
        empty_lists = [lst for lst in analysis.lists if lst.record_count == 0]
        if empty_lists:
            recommendations.append({
                "type": "delete_empty",
                "priority": "high",
                "description": f"Delete {len(empty_lists)} empty lists to reduce costs",
                "lists": [f"{lst.ia}/{lst.io}" for lst in empty_lists],
                "potential_savings": "High - no data loss"
            })

        # Find very old lists with few records
        old_small_lists = [
            lst for lst in analysis.lists
            if lst.record_count < 100 and
            (datetime.now() - lst.created_date).days > 365
        ]
        if old_small_lists:
            recommendations.append({
                "type": "review_old_small",
                "priority": "medium",
                "description": f"Review {len(old_small_lists)} old lists with few records",
                "lists": [f"{lst.ia}/{lst.io}" for lst in old_small_lists],
                "potential_savings": "Medium - review for relevance"
            })

        # Find largest lists for optimization
        large_lists = sorted(analysis.lists, key=lambda x: x.record_count, reverse=True)[:5]
        if large_lists and large_lists[0].record_count > 10000:
            recommendations.append({
                "type": "optimize_large",
                "priority": "low",
                "description": "Consider optimizing largest lists for better performance",
                "lists": [f"{lst.ia}/{lst.io}" for lst in large_lists],
                "potential_savings": "Low - performance improvement"
            })

        return recommendations
