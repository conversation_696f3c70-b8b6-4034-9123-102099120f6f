"""
Data models for Optizmo Lists Analyzer.
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class OptoutList(BaseModel):
    """Model representing an Optizmo optout list."""

    ia: str = Field(..., description="Account/Instance identifier (ia parameter)")
    io: str = Field(..., description="Object/List instance identifier (io parameter)")
    name: str = Field(..., description="Name of the optout list")
    record_count: int = Field(0, description="Number of records in the list")
    created_date: Optional[datetime] = Field(None, description="Date when the list was created")
    last_modified: Optional[datetime] = Field(None, description="Date when the list was last modified")
    description: Optional[str] = Field(None, description="Description of the list")
    list_type: Optional[str] = Field(None, description="Type of the list (Linked List, Regular List, etc.)")
    status: Optional[str] = Field(None, description="Status of the list (active, inactive, etc.)")
    times_accessed: Optional[str] = Field(None, description="Number of times the list has been accessed")
    last_accessed: Optional[str] = Field(None, description="Date when the list was last accessed")
    created_at: Optional[str] = Field(None, description="Date when the list was created (ISO format)")
    times_cleansed: Optional[str] = Field(None, description="Number of times the list has been cleansed")
    downloaded_times: Optional[str] = Field(None, description="Number of times the list has been downloaded")
    opt_out_list_id: Optional[str] = Field(None, description="Optizmo's internal opt-out list ID")


class ListAnalysis(BaseModel):
    """Model representing the analysis results of optout lists."""

    total_lists: int = Field(..., description="Total number of lists analyzed")
    total_records: int = Field(..., description="Total number of records across all lists")
    lists: List[OptoutList] = Field(..., description="List of optout lists sorted by date")
    analysis_date: datetime = Field(default_factory=datetime.now, description="Date when analysis was performed")


class DownloadStatus(BaseModel):
    """Model representing the status of a download operation."""

    status: str = Field(..., description="Status of the download (running, completed, failed)")
    message: Optional[str] = Field(None, description="Status message")
    elapsed_seconds: int = Field(0, description="Elapsed time in seconds")
    added_count: int = Field(0, description="Number of records added")
    malformed_count: int = Field(0, description="Number of malformed records")
    submitted_count: int = Field(0, description="Number of records submitted")


class UploadStatus(BaseModel):
    """Model representing the status of an upload operation."""

    status: str = Field(..., description="Status of the upload (running, completed, failed)")
    message: Optional[str] = Field(None, description="Status message")
    elapsed_seconds: int = Field(0, description="Elapsed time in seconds")
    added_count: int = Field(0, description="Number of records added")
    malformed_count: int = Field(0, description="Number of malformed records")
    submitted_count: int = Field(0, description="Number of records submitted")


class ApiResponse(BaseModel):
    """Generic API response model."""

    success: bool = Field(..., description="Whether the request was successful")
    data: Optional[dict] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if request failed")
    status_code: int = Field(..., description="HTTP status code")
