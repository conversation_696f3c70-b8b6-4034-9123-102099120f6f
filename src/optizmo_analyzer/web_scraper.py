"""
Web scraper for extracting optout list information from Optizmo web interface.
"""

import logging
import time
import re
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

from .config import OptizmoConfig


logger = logging.getLogger(__name__)


class OptizmoWebScraperError(Exception):
    """Custom exception for web scraping errors."""
    pass


class OptizmoWebScraper:
    """Web scraper for Optizmo interface."""

    def __init__(self, config: OptizmoConfig):
        """Initialize the web scraper.

        Args:
            config: Configuration object containing web credentials
        """
        self.config = config
        self.driver: Optional[webdriver.Chrome] = None

        if not config.web_username or not config.web_password:
            raise OptizmoWebScraperError(
                "Web username and password are required for web scraping. "
                "Please set OPTIZMO_WEB_USERNAME and OPTIZMO_WEB_PASSWORD environment variables."
            )

    def __enter__(self):
        """Context manager entry."""
        self._setup_driver()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self._cleanup_driver()

    def _setup_driver(self) -> None:
        """Setup Chrome WebDriver."""
        logger.info("Setting up Chrome WebDriver")

        try:
            # Setup Chrome options
            chrome_options = Options()
            if self.config.headless_browser:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

            # Setup Chrome service
            service = Service(ChromeDriverManager().install())

            # Create driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(self.config.browser_timeout)

            logger.info("Chrome WebDriver setup complete")

        except Exception as e:
            logger.error(f"Failed to setup WebDriver: {e}")
            raise OptizmoWebScraperError(f"Failed to setup WebDriver: {e}")

    def _cleanup_driver(self) -> None:
        """Cleanup WebDriver."""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver cleaned up")
            except Exception as e:
                logger.warning(f"Error cleaning up WebDriver: {e}")

    def login(self) -> None:
        """Login to Optizmo web interface."""
        print("\n🔐 Logging into Optizmo web interface...")
        logger.info("Logging into Optizmo web interface")

        try:
            # Navigate to login page
            login_url = f"{self.config.web_base_url}/client.html?s=Login"
            print(f"🌐 Navigating to login page: {login_url}")
            self.driver.get(login_url)

            # Wait for login form
            wait = WebDriverWait(self.driver, self.config.browser_timeout)
            time.sleep(2)  # Give page time to load

            print(f"✅ Login page loaded. Title: {self.driver.title}")

            # Find and fill username field
            print("🔍 Looking for username field...")
            username_selectors = [
                "input[name='username_client']",  # Optizmo specific
                "input[name='username']",
                "input[name='user']",
                "input[name='login']",
                "input[name='email']",
                "input[type='email']",
                "input[id='username']",
                "input[id='user']",
                "input[id='login']",
                "input[id='email']",
                "#username",
                "#user",
                "#login",
                "#email",
                "input[placeholder*='username']",
                "input[placeholder*='email']",
                "input[placeholder*='login']"
            ]

            username_field = None
            for selector in username_selectors:
                try:
                    username_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    print(f"   ✅ Found username field using: {selector}")
                    break
                except TimeoutException:
                    continue

            if not username_field:
                print("❌ Could not find username/email field")
                print("🔍 Available input fields on page:")
                try:
                    inputs = self.driver.find_elements(By.TAG_NAME, "input")
                    for inp in inputs[:10]:  # Show first 10 inputs
                        name = inp.get_attribute("name") or "(no name)"
                        input_type = inp.get_attribute("type") or "(no type)"
                        placeholder = inp.get_attribute("placeholder") or "(no placeholder)"
                        print(f"   - name='{name}', type='{input_type}', placeholder='{placeholder}'")
                except Exception:
                    pass
                raise OptizmoWebScraperError("Could not find username/email field")

            print(f"📝 Filling username field with: {self.config.web_username}")
            username_field.clear()
            username_field.send_keys(self.config.web_username)

            # Find and fill password field
            print("🔍 Looking for password field...")
            password_selectors = [
                "input[name='password_client']",  # Optizmo specific
                "input[name='password']",
                "input[name='pass']",
                "input[type='password']",
                "input[id='password']",
                "input[id='pass']",
                "#password",
                "#pass",
                "input[placeholder*='password']",
                "input[placeholder*='pass']"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"   ✅ Found password field using: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not password_field:
                print("❌ Could not find password field")
                print("🔍 Available password-type fields on page:")
                try:
                    password_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
                    for inp in password_inputs:
                        name = inp.get_attribute("name") or "(no name)"
                        input_id = inp.get_attribute("id") or "(no id)"
                        print(f"   - name='{name}', id='{input_id}'")
                except Exception:
                    pass
                raise OptizmoWebScraperError("Could not find password field")

            print("🔒 Filling password field...")
            password_field.clear()
            password_field.send_keys(self.config.web_password)

            # Find and click login button
            print("🔍 Looking for login button...")
            login_selectors = [
                "#formSubmit__login",  # Optizmo specific
                "input[value='Login']",  # Optizmo specific
                "button[type='submit']",
                "input[type='submit']",
                "input[value*='Login']",
                "input[value*='Sign In']",
                "input[value*='Submit']",
                "button[value*='Login']",
                "button[value*='Sign In']",
                ".login-button",
                "#login-button",
                "#login",
                "button[name='login']",
                "input[name='login']",
                "button[id*='login']",
                "input[id*='login']"
            ]

            login_button = None
            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"   ✅ Found login button using: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not login_button:
                print("❌ Could not find login button")
                print("🔍 Available buttons and submit elements on page:")
                try:
                    buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
                    all_clickable = buttons + inputs
                    for elem in all_clickable[:10]:  # Show first 10
                        text = elem.text.strip() or elem.get_attribute("value") or "(no text)"
                        elem_type = elem.get_attribute("type") or "button"
                        name = elem.get_attribute("name") or "(no name)"
                        print(f"   - text='{text}', type='{elem_type}', name='{name}'")
                except Exception:
                    pass
                raise OptizmoWebScraperError("Could not find login button")

            print("💆 Clicking login button...")
            login_button.click()

            # Wait for successful login (check for dashboard or lists page)
            print("⏳ Waiting for login to complete...")
            time.sleep(3)

            # Check if login was successful
            current_url = self.driver.current_url
            print(f"🌐 After login - Current URL: {current_url}")

            if "login" in current_url.lower():
                # Check for error messages
                error_selectors = [
                    ".error",
                    ".alert-danger",
                    ".login-error",
                    "[class*='error']"
                ]

                error_message = "Login failed"
                for selector in error_selectors:
                    try:
                        error_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if error_element.is_displayed():
                            error_message = f"Login failed: {error_element.text}"
                            break
                    except NoSuchElementException:
                        continue

                print(f"❌ {error_message}")
                raise OptizmoWebScraperError(error_message)

            print("✅ Successfully logged into Optizmo!")
            logger.info("Successfully logged into Optizmo")

        except OptizmoWebScraperError:
            raise
        except Exception as e:
            logger.error(f"Login failed: {e}")
            raise OptizmoWebScraperError(f"Login failed: {e}")

    def discover_optout_lists(self) -> List[Dict[str, str]]:
        """Discover all optout lists from the web interface.

        Returns:
            List of dictionaries with 'id' and 'name' keys

        Raises:
            OptizmoWebScraperError: If discovery fails
        """
        print("🔍 Discovering optout lists from web interface...")
        logger.info("Discovering optout lists from web interface")

        try:
            # Navigate to optouts page
            optouts_url = f"{self.config.web_base_url}/optouts.html"
            print(f"📄 Navigating to: {optouts_url}")
            self.driver.get(optouts_url)

            # Wait for page to load
            wait = WebDriverWait(self.driver, self.config.browser_timeout)
            time.sleep(2)  # Give page time to fully load

            print(f"✅ Page loaded successfully. Title: {self.driver.title}")
            print(f"🌐 Current URL: {self.driver.current_url}")

            # Look for lists table or container
            lists_selectors = [
                "table",
                ".optout-lists",
                ".lists-table",
                "[class*='list']",
                ".data-table",
                "tbody",
                ".table"
            ]

            lists_container = None
            for selector in lists_selectors:
                try:
                    lists_container = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    print(f"📋 Found lists container using selector: {selector}")
                    break
                except TimeoutException:
                    continue

            if not lists_container:
                print("❌ Could not find lists container on the page")
                print("🔍 Available elements on page:")
                # Log some page structure for debugging
                try:
                    tables = self.driver.find_elements(By.TAG_NAME, "table")
                    print(f"   - Found {len(tables)} table(s)")
                    divs = self.driver.find_elements(By.TAG_NAME, "div")
                    print(f"   - Found {len(divs)} div(s)")
                    forms = self.driver.find_elements(By.TAG_NAME, "form")
                    print(f"   - Found {len(forms)} form(s)")
                except Exception as e:
                    print(f"   - Error checking page elements: {e}")
                raise OptizmoWebScraperError("Could not find lists container on the page")

            discovered_lists = []
            print("\n🔍 Starting list extraction...")

            # Try different approaches to extract list information

            # Approach 1: Look for table rows with list data
            try:
                print("📊 Approach 1: Analyzing table rows...")
                rows = self.driver.find_elements(By.CSS_SELECTOR, "tr")
                print(f"   Found {len(rows)} table rows")

                for row_idx, row in enumerate(rows):
                    # Look for list ID patterns
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) >= 2:
                        row_data = [cell.text.strip() for cell in cells]
                        print(f"   Row {row_idx}: {row_data[:3]}...")  # Show first 3 cells

                        # Check if any cell contains a list ID pattern
                        for i, cell in enumerate(cells):
                            cell_text = cell.text.strip()
                            # List IDs are typically alphanumeric strings (8+ chars)
                            if len(cell_text) > 8 and cell_text.replace('-', '').replace('_', '').isalnum():
                                list_id = cell_text
                                # Try to find name in adjacent cells
                                list_name = f"List {list_id}"
                                if i + 1 < len(cells):
                                    next_cell = cells[i + 1].text.strip()
                                    if next_cell and not next_cell.isdigit() and len(next_cell) > 2:
                                        list_name = next_cell
                                elif i > 0:
                                    prev_cell = cells[i - 1].text.strip()
                                    if prev_cell and not prev_cell.isdigit() and len(prev_cell) > 2:
                                        list_name = prev_cell

                                print(f"   ✅ Found list: ID='{list_id}', Name='{list_name}'")
                                discovered_lists.append({"id": list_id, "name": list_name})
                                break

                print(f"   📊 Table approach found {len(discovered_lists)} lists")
            except Exception as e:
                print(f"   ❌ Table parsing approach failed: {e}")
                logger.debug(f"Table parsing approach failed: {e}")

            # Approach 2: Look for links or elements with list IDs
            try:
                print("\n🔗 Approach 2: Analyzing links and hrefs...")
                links = self.driver.find_elements(By.TAG_NAME, "a")
                print(f"   Found {len(links)} links")

                links_found = 0
                for link in links:
                    href = link.get_attribute("href") or ""
                    text = link.text.strip()

                    # Look for list ID in href or text
                    list_id_pattern = r'[a-f0-9]{12,}|[A-Z0-9]{8,}'

                    match = re.search(list_id_pattern, href)
                    if match:
                        list_id = match.group()
                        list_name = text if text else f"List {list_id}"
                        print(f"   ✅ Found list in href: ID='{list_id}', Name='{list_name}'")
                        discovered_lists.append({"id": list_id, "name": list_name})
                        links_found += 1
                        continue

                    match = re.search(list_id_pattern, text)
                    if match:
                        list_id = match.group()
                        list_name = text.replace(list_id, "").strip() or f"List {list_id}"
                        print(f"   ✅ Found list in text: ID='{list_id}', Name='{list_name}'")
                        discovered_lists.append({"id": list_id, "name": list_name})
                        links_found += 1

                print(f"   🔗 Link approach found {links_found} additional lists")
            except Exception as e:
                print(f"   ❌ Link parsing approach failed: {e}")
                logger.debug(f"Link parsing approach failed: {e}")

            # Approach 3: Look for specific Optizmo list elements
            try:
                # Look for elements with data attributes or specific classes
                list_elements = self.driver.find_elements(By.CSS_SELECTOR, "[data-list-id], [data-id], .list-item, .optout-list")
                for element in list_elements:
                    list_id = (element.get_attribute("data-list-id") or
                              element.get_attribute("data-id") or
                              element.get_attribute("id"))

                    if list_id:
                        list_name = element.text.strip() or f"List {list_id}"
                        discovered_lists.append({"id": list_id, "name": list_name})

            except Exception as e:
                logger.debug(f"Data attribute parsing approach failed: {e}")

            # Remove duplicates
            seen_ids = set()
            unique_lists = []
            for lst in discovered_lists:
                if lst["id"] not in seen_ids:
                    seen_ids.add(lst["id"])
                    unique_lists.append(lst)

            if not unique_lists:
                print("\n❌ No lists found on this page")
                print(f"   Page title: {self.driver.title}")
                print(f"   Current URL: {self.driver.current_url}")

                # Save page source for debugging
                try:
                    with open("optizmo_page_debug.html", "w") as f:
                        f.write(self.driver.page_source)
                    print("   💾 Page source saved to optizmo_page_debug.html for debugging")
                except Exception:
                    pass

                # Don't raise error, return empty list to allow pagination to continue
                print("   🔄 Returning empty list (pagination will continue)")
                return []

            print(f"\n✅ Successfully discovered {len(unique_lists)} optout lists on this page:")
            for i, lst in enumerate(unique_lists, 1):
                print(f"   {i}. ID: {lst['id'][:20]}... | Name: {lst['name'][:50]}...")

            logger.info(f"Discovered {len(unique_lists)} optout lists")
            return unique_lists

        except OptizmoWebScraperError:
            raise
        except Exception as e:
            logger.error(f"Failed to discover lists: {e}")
            raise OptizmoWebScraperError(f"Failed to discover lists: {e}")

    def discover_optout_lists_improved(self) -> List[Dict[str, str]]:
        """Improved method to discover optout lists using the specific Optizmo table structure.

        Returns:
            List of dictionaries with 'id' and 'name' keys

        Raises:
            OptizmoWebScraperError: If discovery fails
        """
        print("🔍 Using improved extraction method...")

        try:
            discovered_lists = []

            # Wait for the table to load
            wait = WebDriverWait(self.driver, self.config.browser_timeout)

            # Look for the specific table structure
            try:
                table = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "table.list.campaign")))
                print(f"   ✅ Found main table")
            except TimeoutException:
                print(f"   ❌ Could not find main table, trying tbody")
                try:
                    table = wait.until(EC.presence_of_element_located((By.TAG_NAME, "tbody")))
                    print(f"   ✅ Found tbody")
                except TimeoutException:
                    print(f"   ❌ Could not find table structure")
                    return []

            # Find all table rows
            rows = table.find_elements(By.TAG_NAME, "tr")
            print(f"   📊 Found {len(rows)} table rows")

            for row_idx, row in enumerate(rows):
                try:
                    # Skip header and footer rows
                    if row.get_attribute("id") and ("header" in row.get_attribute("id") or "footer" in row.get_attribute("id")):
                        continue

                    # Look for the name cell (first td with class="name")
                    name_cell = row.find_element(By.CSS_SELECTOR, "td.name")

                    # Find the link inside the name cell
                    link = name_cell.find_element(By.TAG_NAME, "a")
                    href = link.get_attribute("href")

                    if not href:
                        continue

                    # Extract ia and io parameters from href
                    # Example: optouts.html&ia=031c127ed9777610&io=0009685e1d3824dc&s=View
                    ia_match = re.search(r'ia=([a-f0-9]+)', href)
                    io_match = re.search(r'io=([a-f0-9]+)', href)

                    if ia_match and io_match:
                        ia_param = ia_match.group(1)
                        io_param = io_match.group(1)

                        # Extract list name from the link text
                        full_link_text = link.text.strip()

                        # Check if it's a linked list
                        is_linked_list = "Linked List" in full_link_text
                        list_type = "Linked List" if is_linked_list else "Regular List"

                        # Clean up the name (remove extra whitespace and "Linked List" indicators)
                        list_name = re.sub(r'\s+', ' ', full_link_text)  # Normalize whitespace
                        list_name = re.sub(r'\s*Linked List\s*', '', list_name)  # Remove "Linked List"
                        list_name = list_name.strip()

                        if not list_name:
                            list_name = f"List {io_param}"

                        # Try to get record count from the same row
                        record_count = "Unknown"
                        try:
                            record_cell = row.find_element(By.CSS_SELECTOR, "td.optoutsfull")
                            record_count = record_cell.text.strip().replace(',', '')
                        except NoSuchElementException:
                            pass

                        # Try to get access information from the same row
                        times_accessed = "Unknown"
                        last_accessed = "Unknown"
                        try:
                            # Look for the access cell (usually contains "X Times" and date)
                            access_cells = row.find_elements(By.TAG_NAME, "td")
                            for cell in access_cells:
                                cell_text = cell.text.strip()
                                if "Times" in cell_text:
                                    # Extract times accessed (e.g., "74 Times")
                                    times_match = re.search(r'(\d+)\s+Times', cell_text)
                                    if times_match:
                                        times_accessed = times_match.group(1)

                                    # Extract last accessed date from div inside the cell
                                    try:
                                        date_div = cell.find_element(By.CSS_SELECTOR, "div.small")
                                        date_text = date_div.text.strip()
                                        # Extract date from "Last Oct 19, 24" format
                                        date_match = re.search(r'Last\s+(.+)', date_text)
                                        if date_match:
                                            raw_date = date_match.group(1)
                                            # Convert to ISO format (e.g., "May 23, 25" -> "2025-05-23")
                                            last_accessed = self._parse_optizmo_date(raw_date)
                                    except NoSuchElementException:
                                        pass
                                    break
                        except Exception:
                            pass

                        # Store basic info first (we'll get details later to avoid stale element issues)
                        discovered_lists.append({
                            "ia": ia_param,
                            "io": io_param,
                            "name": list_name,
                            "type": list_type,
                            "record_count": record_count,
                            "times_accessed": times_accessed,
                            "last_accessed": last_accessed,
                            "created_at": "Unknown",  # Will be filled later
                            "times_cleansed": "Unknown",  # Will be filled later
                            "opt_out_list_id": "Unknown",  # Will be filled later
                            "downloaded_times": "Unknown"  # Will be filled later
                        })

                        print(f"   ✅ Row {row_idx}: IA='{ia_param[:8]}...', IO='{io_param[:8]}...', Name='{list_name[:25]}...', Type={list_type}, Records={record_count}, Accessed={times_accessed}x, LastAccess={last_accessed}")

                except NoSuchElementException:
                    # This row doesn't have the expected structure, skip it
                    continue
                except Exception as e:
                    print(f"   ⚠️ Error processing row {row_idx}: {e}")
                    continue

            print(f"\n✅ Improved extraction found {len(discovered_lists)} lists on this page")
            return discovered_lists

        except Exception as e:
            print(f"   ❌ Improved extraction failed: {e}")
            logger.error(f"Improved list discovery failed: {e}")
            # Fall back to the original method
            print(f"   🔄 Falling back to original method...")
            return self.discover_optout_lists()

    def navigate_and_discover_with_pagination(self) -> List[Dict[str, str]]:
        """Navigate through paginated lists and discover all optout lists.

        Returns:
            List of dictionaries with 'id' and 'name' keys

        Raises:
            OptizmoWebScraperError: If discovery fails
        """
        logger.info("Discovering lists with pagination support")

        all_lists = []
        page_offset = 0  # Start with page 0 (first page)
        max_pages = 2  # Limit to 2 pages for testing detail extraction
        page_size = 70  # Based on your URL pattern

        try:
            page_num = 1
            while page_num <= max_pages:
                print(f"\n📄 ===== PROCESSING PAGE {page_num} (offset={page_offset}) =====")
                logger.info(f"Processing page {page_num} with offset {page_offset}")

                # Navigate directly to the specific page URL
                page_url = f"{self.config.web_base_url}/optouts.html?page={page_offset}"
                print(f"🌐 Navigating to: {page_url}")
                self.driver.get(page_url)
                time.sleep(2)  # Wait for page to load

                # Discover lists on current page using improved extraction
                print(f"   🔍 About to call discover_optout_lists_improved()...")
                page_lists = self.discover_optout_lists_improved()
                print(f"   ✅ discover_optout_lists_improved() returned {len(page_lists) if page_lists else 0} lists")

                if not page_lists:
                    print(f"   🚫 No lists found on page {page_num}, stopping pagination")
                    logger.info("No lists found on current page, stopping pagination")
                    break

                print(f"   📊 Page {page_num} contributed {len(page_lists)} lists")
                print(f"   🔍 About to extend all_lists...")
                all_lists.extend(page_lists)
                print(f"   ✅ all_lists now has {len(all_lists)} total lists")
                print(f"   🔍 About to check for next page...")

                # Check if there's a next page using the PNnext element
                has_next_page = self._check_for_next_page()
                print(f"   ✅ _check_for_next_page() returned: {has_next_page}")
                if not has_next_page:
                    print(f"   🏁 Reached the last page (page {page_num}), stopping pagination")
                    logger.info("No next page available, stopping pagination")
                    break

                # Move to next page
                page_offset += page_size
                page_num += 1

            # Keep ALL lists (no deduplication as requested by user)
            print(f"\n🔄 Keeping ALL {len(all_lists)} lists (no deduplication)...")
            unique_lists = all_lists  # Keep all lists

            print(f"\n🎉 ===== DISCOVERY COMPLETE =====")
            print(f"📊 Total pages processed: {page_num-1}")
            print(f"📊 Total lists found: {len(all_lists)}")
            print(f"📊 All lists kept (no deduplication): {len(unique_lists)}")

            if unique_lists:
                print(f"\n📋 Final list summary:")
                for i, lst in enumerate(unique_lists[:10], 1):  # Show first 10
                    print(f"   {i}. {lst['name'][:50]}... (IA: {lst['ia'][:8]}..., IO: {lst['io'][:8]}...)")
                if len(unique_lists) > 10:
                    print(f"   ... and {len(unique_lists) - 10} more lists")

            logger.info(f"Discovered {len(unique_lists)} unique lists across {page_num-1} pages")
            print(f"\n📝 DEBUG: About to start second pass...")
            print(f"   📝 DEBUG: unique_lists length = {len(unique_lists)}")
            print(f"   📝 DEBUG: test_limit will be = {min(10, len(unique_lists))}")

            # Second pass: get additional details for each list
            print(f"\n🔍 ===== GETTING ADDITIONAL DETAILS =====\n")
            test_limit = min(10, len(unique_lists))  # Limit to 10 for testing
            print(f"   🔍 Getting additional details for {test_limit} lists (testing)...")

            for i, list_info in enumerate(unique_lists[:test_limit], 1):
                try:
                    print(f"     🔍 ({i}/{test_limit}) Getting details for: {list_info['name'][:30]}...")
                    additional_details = self._get_list_details(list_info['ia'], list_info['io'])

                    # Update the list info with additional details
                    list_info['created_at'] = additional_details['created_at']
                    list_info['times_cleansed'] = additional_details['times_cleansed']
                    list_info['opt_out_list_id'] = additional_details['opt_out_list_id']
                    list_info['downloaded_times'] = additional_details['downloaded_times']

                    print(f"     ✅ Updated: Created={additional_details['created_at']}, Cleansed={additional_details['times_cleansed']}x, Downloaded={additional_details['downloaded_times']}x, OptOutID={additional_details['opt_out_list_id'][:12]}...")

                except Exception as e:
                    print(f"     ⚠️ Error getting details for {list_info['name'][:30]}: {e}")
                    # Keep the "Unknown" values
                    continue

            # Show a sample of the enhanced data
            print(f"\n📋 Sample of enhanced list data:")
            for i, list_info in enumerate(unique_lists[:10], 1):  # Show first 10 lists
                print(f"   {i}. {list_info['name'][:40]}...")
                print(f"      IA: {list_info['ia']}, IO: {list_info['io']}")
                print(f"      Type: {list_info['type']}, Records: {list_info['record_count']}")
                print(f"      Created: {list_info['created_at']}, Cleansed: {list_info['times_cleansed']}x, Downloaded: {list_info['downloaded_times']}x")
                print(f"      Accessed: {list_info['times_accessed']}x, Last: {list_info['last_accessed']}")
                print(f"      OptOut ID: {list_info['opt_out_list_id']}")
                print()

            # Debug: Show sample of final data before returning
            print(f"\n🔍 DEBUG: Final data sample before returning to analyzer:")
            for i, lst in enumerate(unique_lists[:3], 1):
                print(f"   {i}. {lst['name'][:30]}...")
                print(f"      IA: {lst['ia']}, IO: {lst['io']}")
                print(f"      created_at: '{lst.get('created_at', 'NOT_SET')}'")
                print(f"      times_cleansed: '{lst.get('times_cleansed', 'NOT_SET')}'")
                print(f"      downloaded_times: '{lst.get('downloaded_times', 'NOT_SET')}'")
                print(f"      opt_out_list_id: '{lst.get('opt_out_list_id', 'NOT_SET')}'")
                print()

            print(f"\n✅ Successfully completed enhanced list discovery with {len(unique_lists)} lists")
            return unique_lists

        except Exception as e:
            logger.error(f"Pagination discovery failed: {e}")
            raise OptizmoWebScraperError(f"Pagination discovery failed: {e}")

    def _check_for_next_page(self) -> bool:
        """Check if there's a next page available using the PNnext element.

        Returns:
            True if next page is available, False otherwise
        """
        try:
            print(f"   🔍 Checking for next page...")

            # Look for the PNnext element
            next_page_element = self.driver.find_element(By.CSS_SELECTOR, "li.PNnext")

            if next_page_element:
                # Check if it contains a link
                try:
                    next_link = next_page_element.find_element(By.TAG_NAME, "a")
                    next_url = next_link.get_attribute("href")
                    print(f"   ✅ Next page found: {next_url}")
                    return True
                except NoSuchElementException:
                    print(f"   🚫 PNnext element found but no link inside")
                    return False
            else:
                print(f"   🚫 No PNnext element found")
                return False

        except NoSuchElementException:
            print(f"   🚫 No PNnext element found - reached last page")
            return False
        except Exception as e:
            print(f"   ⚠️ Error checking for next page: {e}")
            logger.warning(f"Error checking for next page: {e}")
            return False

    def _parse_optizmo_date(self, date_str: str) -> str:
        """Parse Optizmo date format to ISO format.

        Args:
            date_str: Date string like "May 23, 25" or "Oct 19, 24"

        Returns:
            ISO formatted date string like "2025-05-23" or "Unknown" if parsing fails
        """
        try:
            # Handle formats like "May 23, 25" or "Oct 19, 24"
            # The year is in 2-digit format, so we need to convert it to 4-digit

            # Split by comma to separate month/day from year
            parts = date_str.split(',')
            if len(parts) != 2:
                return "Unknown"

            month_day = parts[0].strip()  # "May 23"
            year_short = parts[1].strip()  # "25"

            # Convert 2-digit year to 4-digit (assuming 20xx)
            year_full = f"20{year_short}"

            # Parse the month and day
            from datetime import datetime
            try:
                # Try to parse "May 23" format
                parsed_date = datetime.strptime(f"{month_day} {year_full}", "%b %d %Y")
                return parsed_date.strftime("%Y-%m-%d")
            except ValueError:
                # If that fails, try other common formats
                try:
                    parsed_date = datetime.strptime(f"{month_day} {year_full}", "%B %d %Y")
                    return parsed_date.strftime("%Y-%m-%d")
                except ValueError:
                    return "Unknown"

        except Exception as e:
            print(f"   ⚠️ Error parsing date '{date_str}': {e}")
            return "Unknown"

    def _get_list_details(self, ia: str, io: str) -> dict:
        """Get additional details for a specific list by navigating to its detail page.

        Args:
            ia: Account/Instance identifier
            io: Object/List instance identifier

        Returns:
            Dictionary with additional details: created_at, times_cleansed, opt_out_list_id, downloaded_times
        """
        import re  # Import re module at the beginning

        details = {
            "created_at": "Unknown",
            "times_cleansed": "Unknown",
            "opt_out_list_id": "Unknown",
            "downloaded_times": "Unknown"
        }

        try:
            # Navigate to the list details page
            detail_url = f"{self.config.web_base_url}/optouts.html&ia={ia}&io={io}&s=View"
            print(f"     🔍 Getting details from: {detail_url}")

            self.driver.get(detail_url)
            time.sleep(3)  # Wait longer for page to load completely

            # Look for the details box that contains the detail fields
            try:
                # Wait for any details box to be present
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.box2.boxDetails"))
                )

                # Find all boxes with the boxDetails class
                all_details_boxes = self.driver.find_elements(By.CSS_SELECTOR, "div.box2.boxDetails")
                print(f"     📋 Found {len(all_details_boxes)} div.box2.boxDetails elements")

                # Find the one that contains our target fields
                details_box = None
                for i, box in enumerate(all_details_boxes):
                    box_html = box.get_attribute('outerHTML')
                    # Check if this box contains the fields we're looking for
                    if 'Created :' in box_html and 'Downloaded :' in box_html and 'Cleansed :' in box_html:
                        print(f"     ✅ Found correct details box (box {i+1}) with target fields")
                        details_box = box
                        break
                    else:
                        box_class = box.get_attribute('class')
                        print(f"     🔍 Box {i+1} (class='{box_class}') doesn't contain target fields")

                if not details_box:
                    print(f"     ❌ No details box found with target fields")
                    return details

                print(f"     ✅ Using correct details box")

                # Debug: Show the entire details box HTML
                try:
                    details_html = details_box.get_attribute('outerHTML')
                    print(f"     🔍 Details box HTML (first 1000 chars): {details_html[:1000]}...")
                except:
                    pass

                # Look for all tables in the details box
                tables = details_box.find_elements(By.TAG_NAME, "table")
                print(f"     📋 Found {len(tables)} table elements in details box")

                # Find the table that contains th/td structure (detail fields)
                detail_table = None
                for i, table in enumerate(tables):
                    try:
                        table_html = table.get_attribute('outerHTML')
                        print(f"     🔍 Table {i+1} HTML (first 300 chars): {table_html[:300]}...")

                        # Check if this table has th elements (indicating it's the details table)
                        th_elements = table.find_elements(By.TAG_NAME, "th")
                        if th_elements:
                            print(f"     ✅ Table {i+1} has {len(th_elements)} th elements - this looks like the details table!")
                            detail_table = table
                            break
                        else:
                            print(f"     🔍 Table {i+1} has no th elements - skipping")
                    except Exception as e:
                        print(f"     ⚠️ Error analyzing table {i+1}: {e}")

                if not detail_table:
                    print(f"     ⚠️ No table with th elements found - looking for any table with tr elements")
                    # Fallback: look for any table with multiple rows
                    for i, table in enumerate(tables):
                        try:
                            rows = table.find_elements(By.TAG_NAME, "tr")
                            if len(rows) > 1:  # More than just a header
                                print(f"     🔍 Using table {i+1} as fallback (has {len(rows)} rows)")
                                detail_table = table
                                break
                        except:
                            continue

                if not detail_table:
                    print(f"     ❌ No suitable table found in details box")

                    # Alternative approach: search for field names anywhere in the details box
                    print(f"     🔍 Trying alternative approach: searching for field names in details box text")
                    details_text = details_box.text
                    print(f"     🔍 Details box text: {details_text[:500]}...")

                    # Look for specific field patterns in the text
                    field_patterns = {
                        'created': r'Created\s*:?\s*([^\n\r]+)',
                        'downloaded': r'Downloaded\s*:?\s*([^\n\r]+)',
                        'cleansed': r'Cleansed\s*:?\s*([^\n\r]+)',
                        'opt_out_list_id': r'Opt-?Out\s+List\s+Id\s*:?\s*([^\n\r]+)'
                    }

                    import re
                    for field_name, pattern in field_patterns.items():
                        match = re.search(pattern, details_text, re.IGNORECASE)
                        if match:
                            value = match.group(1).strip()
                            print(f"     ✅ Found {field_name} in text: '{value}'")

                            if field_name == 'created':
                                details['created_at'] = self._parse_optizmo_date_full(value)
                            elif field_name == 'downloaded':
                                downloaded_match = re.search(r'(\d+)\s+times?', value, re.IGNORECASE)
                                if downloaded_match:
                                    details['downloaded_times'] = downloaded_match.group(1)
                            elif field_name == 'cleansed':
                                cleansed_match = re.search(r'(\d+)\s+times?', value, re.IGNORECASE)
                                if cleansed_match:
                                    details['times_cleansed'] = cleansed_match.group(1)
                            elif field_name == 'opt_out_list_id':
                                details['opt_out_list_id'] = value
                        else:
                            print(f"     ⚠️ Field {field_name} not found in text")

                    return details

                # Try to find tbody, but also check if rows are directly in table
                try:
                    tbody = detail_table.find_element(By.TAG_NAME, "tbody")
                    rows = tbody.find_elements(By.TAG_NAME, "tr")
                    print(f"     📋 Found {len(rows)} detail rows in tbody")
                except:
                    print(f"     🔍 No tbody found, checking for rows directly in table")
                    rows = detail_table.find_elements(By.TAG_NAME, "tr")
                    print(f"     📋 Found {len(rows)} detail rows directly in table")

                for i, row in enumerate(rows, 1):
                    try:
                        # Try to find th and td elements
                        th_elements = row.find_elements(By.TAG_NAME, "th")
                        td_elements = row.find_elements(By.TAG_NAME, "td")

                        # Debug: Show what we actually found in this row
                        print(f"     🔍 Row {i} structure: th={len(th_elements)}, td={len(td_elements)}")
                        if th_elements:
                            print(f"     🔍 TH texts: {[th.text.strip() for th in th_elements]}")
                        if td_elements:
                            print(f"     🔍 TD texts: {[td.text.strip() for td in td_elements]}")

                        # Also check the raw HTML of this row for debugging
                        try:
                            row_html = row.get_attribute('outerHTML')
                            print(f"     🔍 Row HTML: {row_html[:200]}...")
                        except:
                            pass

                        if len(th_elements) >= 1 and len(td_elements) >= 1:
                            field_name_raw = th_elements[0].text.strip()
                            field_value = td_elements[0].text.strip()

                            # Normalize field name for comparison (remove colons, convert to lowercase)
                            field_name = field_name_raw.lower().replace(':', '').strip()

                            print(f"     🔎 Row {i}: '{field_name_raw}' = '{field_value}'")
                            print(f"     🔍 Normalized field name: '{field_name}'")

                            # Process the extracted field based on normalized field name
                            if field_name == "created":
                                # Parse date like "Dec 15, 2023" to "2023-12-15"
                                parsed_date = self._parse_optizmo_date_full(field_value)
                                details["created_at"] = parsed_date
                                print(f"     ✅ Parsed created date: '{field_value}' -> '{parsed_date}'")

                            elif field_name == "cleansed":
                                # Extract number from "1 times" or "5 times"
                                cleansed_match = re.search(r'(\d+)\s+times?', field_value, re.IGNORECASE)
                                if cleansed_match:
                                    details["times_cleansed"] = cleansed_match.group(1)
                                    print(f"     ✅ Parsed cleansed times: '{field_value}' -> '{cleansed_match.group(1)}'")
                                else:
                                    print(f"     ⚠️ Could not parse cleansed times from: '{field_value}'")

                            elif field_name == "downloaded":
                                # Extract number from "73 times" or "5 times"
                                downloaded_match = re.search(r'(\d+)\s+times?', field_value, re.IGNORECASE)
                                if downloaded_match:
                                    details["downloaded_times"] = downloaded_match.group(1)
                                    print(f"     ✅ Parsed downloaded times: '{field_value}' -> '{downloaded_match.group(1)}'")
                                else:
                                    print(f"     ⚠️ Could not parse downloaded times from: '{field_value}'")

                            elif field_name in ["opt-out list id", "opt out list id", "optout list id"]:
                                details["opt_out_list_id"] = field_value
                                print(f"     ✅ Found opt-out list ID: '{field_value}'")

                            else:
                                print(f"     🔍 Skipping field: '{field_name}' (not needed)")
                        else:
                            print(f"     ⚠️ Row {i}: Invalid structure - th: {len(th_elements)}, td: {len(td_elements)}")

                    except Exception as e:
                        print(f"     ⚠️ Error processing row {i}: {e}")
                        continue

            except Exception as e:
                print(f"     ⚠️ Could not find details box: {e}")
                # Let's try to see what's on the page
                try:
                    page_title = self.driver.title
                    print(f"     📝 Page title: {page_title}")
                    # Check if we're on the right page
                    if "optout" not in page_title.lower():
                        print(f"     ⚠️ Might be on wrong page")

                    # Try to find any div with "box" class for debugging
                    try:
                        all_boxes = self.driver.find_elements(By.CSS_SELECTOR, "div[class*='box']")
                        print(f"     🔍 Found {len(all_boxes)} div elements with 'box' in class name")
                        for i, box in enumerate(all_boxes[:3]):  # Show first 3
                            box_class = box.get_attribute('class')
                            box_text = box.text[:100] if box.text else "(no text)"
                            print(f"       Box {i+1}: class='{box_class}', text='{box_text}...'")
                    except Exception as box_e:
                        print(f"     ⚠️ Could not find any box elements: {box_e}")

                    # Try to find the specific structure we're looking for
                    try:
                        # Look for any table with th/td structure
                        tables = self.driver.find_elements(By.TAG_NAME, "table")
                        print(f"     🔍 Found {len(tables)} table elements on page")
                        for i, table in enumerate(tables[:2]):  # Show first 2 tables
                            rows = table.find_elements(By.TAG_NAME, "tr")
                            print(f"       Table {i+1}: {len(rows)} rows")
                            for j, row in enumerate(rows[:3]):  # Show first 3 rows
                                ths = row.find_elements(By.TAG_NAME, "th")
                                tds = row.find_elements(By.TAG_NAME, "td")
                                if ths and tds:
                                    th_text = ths[0].text.strip() if ths else "(no th)"
                                    td_text = tds[0].text.strip() if tds else "(no td)"
                                    print(f"         Row {j+1}: '{th_text}' = '{td_text}'")
                    except Exception as table_e:
                        print(f"     ⚠️ Could not analyze table structure: {table_e}")

                except Exception as debug_e:
                    print(f"     ⚠️ Error during page debugging: {debug_e}")

        except Exception as e:
            print(f"     ❌ Error getting list details: {e}")

        return details

    def _parse_optizmo_date_full(self, date_str: str) -> str:
        """Parse full Optizmo date format to ISO format.

        Args:
            date_str: Date string like "May 17, 2023" (full year format)

        Returns:
            ISO formatted date string like "2023-05-17" or "Unknown" if parsing fails
        """
        try:
            from datetime import datetime

            # Try different date formats
            formats_to_try = [
                "%B %d, %Y",    # "May 17, 2023"
                "%b %d, %Y",     # "May 17, 2023" (abbreviated month)
                "%m/%d/%Y",      # "05/17/2023"
                "%Y-%m-%d",      # "2023-05-17" (already ISO)
            ]

            for fmt in formats_to_try:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime("%Y-%m-%d")
                except ValueError:
                    continue

            # If none of the formats work, return Unknown
            return "Unknown"

        except Exception as e:
            print(f"     ⚠️ Error parsing full date '{date_str}': {e}")
            return "Unknown"
