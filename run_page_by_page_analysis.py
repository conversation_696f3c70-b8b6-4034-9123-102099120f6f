#!/usr/bin/env python3
"""
Page-by-Page Optizmo Analysis - Process and save each page immediately
"""

import sys
import csv
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.analyzer import OptizmoAnalyzer
from optizmo_analyzer.models import OptoutList
from optizmo_analyzer.web_scraper import OptizmoWebScraper


class PageByPageAnalyzer:
    """Analyzer that processes and saves each page immediately."""

    def __init__(self, csv_filename: str = None):
        self.csv_filename = csv_filename or f"optizmo_page_by_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.fieldnames = [
            'ia', 'io', 'list_name', 'list_type', 'items_count',
            'times_accessed', 'last_accessed', 'created_at',
            'times_cleansed', 'downloaded_times', 'opt_out_list_id'
        ]
        self.processed_lists = set()  # Track processed IA+IO combinations
        self.last_page_processed = -1

        # Load existing data if file exists
        self._load_existing_data()

    def _load_existing_data(self):
        """Load existing CSV data to determine where to resume."""
        if not os.path.exists(self.csv_filename):
            print(f"📄 Starting fresh analysis - CSV file will be: {self.csv_filename}")
            return

        print(f"📄 Found existing CSV file: {self.csv_filename}")

        try:
            with open(self.csv_filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                count = 0
                for row in reader:
                    ia_io = f"{row['ia']}_{row['io']}"
                    self.processed_lists.add(ia_io)
                    count += 1

                print(f"   📊 Found {count} existing records")
                print(f"   🔄 Will skip already processed lists and continue from where left off")

        except Exception as e:
            print(f"   ⚠️  Error reading existing CSV: {e}")
            print(f"   🔄 Will start fresh")

    def _initialize_csv(self):
        """Initialize CSV file with headers if it doesn't exist."""
        if not os.path.exists(self.csv_filename):
            with open(self.csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
                writer.writeheader()
                print(f"   ✅ CSV file initialized: {self.csv_filename}")

    def _save_page_data(self, optout_lists: List[OptoutList], page_num: int):
        """Save a page of analyzed data to CSV immediately."""
        if not optout_lists:
            print(f"   ⚠️  Page {page_num}: No data to save")
            return 0

        new_records = 0
        skipped_records = 0

        # Open file in append mode and write immediately
        with open(self.csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)

            for optout_list in optout_lists:
                ia_io = f"{optout_list.ia}_{optout_list.io}"

                # Skip if already processed
                if ia_io in self.processed_lists:
                    skipped_records += 1
                    continue

                # Process field values
                list_type = getattr(optout_list, 'list_type', 'Unknown')
                if not list_type:
                    list_type = 'Unknown'

                times_accessed = getattr(optout_list, 'times_accessed', '0')
                if not times_accessed or times_accessed == 'Unknown':
                    times_accessed = '0'

                last_accessed = getattr(optout_list, 'last_accessed', 'Never')
                if not last_accessed or last_accessed == 'Unknown':
                    last_accessed = 'Never'

                created_at = getattr(optout_list, 'created_at', 'Never')
                if not created_at or created_at == 'Unknown':
                    created_at = 'Never'

                times_cleansed = getattr(optout_list, 'times_cleansed', '0')
                if not times_cleansed or times_cleansed == 'Unknown':
                    times_cleansed = '0'

                downloaded_times = getattr(optout_list, 'downloaded_times', '0')
                if not downloaded_times or downloaded_times == 'Unknown':
                    downloaded_times = '0'

                opt_out_list_id = getattr(optout_list, 'opt_out_list_id', 'Unknown')
                if not opt_out_list_id:
                    opt_out_list_id = 'Unknown'

                # Write row immediately
                writer.writerow({
                    'ia': optout_list.ia,
                    'io': optout_list.io,
                    'list_name': optout_list.name,
                    'list_type': list_type,
                    'items_count': optout_list.record_count,
                    'times_accessed': times_accessed,
                    'last_accessed': last_accessed,
                    'created_at': created_at,
                    'times_cleansed': times_cleansed,
                    'downloaded_times': downloaded_times,
                    'opt_out_list_id': opt_out_list_id
                })

                # Track as processed
                self.processed_lists.add(ia_io)
                new_records += 1

            # Force flush to disk before closing
            csvfile.flush()
            if hasattr(csvfile, 'fileno'):
                os.fsync(csvfile.fileno())

        print(f"   💾 Page {page_num}: Saved {new_records} new records to disk, skipped {skipped_records} duplicates")

        # Verify data was written
        try:
            with open(self.csv_filename, 'r', encoding='utf-8') as verify_file:
                reader = csv.DictReader(verify_file)
                total_rows = sum(1 for row in reader)
                print(f"   ✅ Verification: CSV now contains {total_rows} total records")
        except Exception as e:
            print(f"   ⚠️  Could not verify CSV: {e}")

        return new_records

    def _get_list_details_from_web(self, ia: str, io: str) -> Dict[str, str]:
        """Get additional details for a list from the web interface."""
        try:
            # Use the global web scraper that's already logged in
            if hasattr(self, '_web_scraper') and self._web_scraper:
                # Use the correct method from OptizmoWebScraper
                details = self._web_scraper._get_list_details(ia, io)
                return {
                    'created_at': details.get('created_at', 'Unknown'),
                    'times_cleansed': details.get('times_cleansed', 'Unknown'),
                    'downloaded_times': details.get('downloaded_times', 'Unknown'),
                    'opt_out_list_id': details.get('opt_out_list_id', 'Unknown')
                }
            else:
                print(f"     ⚠️  Web scraper not available for details extraction")
                return {
                    'created_at': 'Unknown',
                    'times_cleansed': 'Unknown',
                    'downloaded_times': 'Unknown',
                    'opt_out_list_id': 'Unknown'
                }
        except Exception as e:
            print(f"     ❌ Error getting details for IA={ia}, IO={io}: {e}")
            return {
                'created_at': 'Unknown',
                'times_cleansed': 'Unknown',
                'downloaded_times': 'Unknown',
                'opt_out_list_id': 'Unknown'
            }

    def _process_single_page(self, analyzer: OptizmoAnalyzer, page_lists: List[Dict], page_num: int) -> int:
        """Process a single page: discover + analyze + save immediately."""
        if not page_lists:
            print(f"   ⚠️  Page {page_num}: No lists to process")
            return 0

        print(f"\n📊 PROCESSING PAGE {page_num}")
        print(f"   📋 Lists discovered: {len(page_lists)}")

        # Filter out already processed lists
        new_lists = []
        skipped_count = 0

        for list_info in page_lists:
            ia_io = f"{list_info['ia']}_{list_info['io']}"
            if ia_io not in self.processed_lists:
                new_lists.append(list_info)
            else:
                skipped_count += 1

        if skipped_count > 0:
            print(f"   🔄 Skipping {skipped_count} already processed lists")

        if not new_lists:
            print(f"   ✅ Page {page_num}: All lists already processed")
            return 0

        print(f"   🔍 Analyzing {len(new_lists)} new lists...")

        try:
            # Use the web scraper to get detailed information for each list
            # This will extract created_at, times_cleansed, downloaded_times, opt_out_list_id
            print(f"   🔍 Starting detailed analysis (Phase 2) for {len(new_lists)} lists...")

            analyzed_lists = []
            for i, list_info in enumerate(new_lists, 1):
                try:
                    print(f"     🔍 ({i}/{len(new_lists)}) Getting details for: {list_info['name'][:30]}...")

                    # Get additional details from web scraper
                    additional_details = self._get_list_details_from_web(list_info['ia'], list_info['io'])

                    # Update the list info with additional details
                    list_info['created_at'] = additional_details['created_at']
                    list_info['times_cleansed'] = additional_details['times_cleansed']
                    list_info['opt_out_list_id'] = additional_details['opt_out_list_id']
                    list_info['downloaded_times'] = additional_details['downloaded_times']

                    print(f"     ✅ Updated: Created={additional_details['created_at']}, Cleansed={additional_details['times_cleansed']}x, Downloaded={additional_details['downloaded_times']}x, OptOutID={additional_details['opt_out_list_id'][:12] if additional_details['opt_out_list_id'] != 'Unknown' else 'Unknown'}...")

                    # Create OptoutList object with all the details
                    optout_list = OptoutList(
                        ia=list_info['ia'],
                        io=list_info['io'],
                        name=list_info['name'],
                        record_count=int(list_info.get('record_count', 0)),
                        created_date=datetime.now(),
                        list_type=list_info.get('type', 'Unknown'),
                        times_accessed=list_info.get('times_accessed', '0'),
                        last_accessed=list_info.get('last_accessed', 'Never'),
                        created_at=additional_details['created_at'],
                        times_cleansed=additional_details['times_cleansed'],
                        downloaded_times=additional_details['downloaded_times'],
                        opt_out_list_id=additional_details['opt_out_list_id'],
                        status="active"
                    )

                    analyzed_lists.append(optout_list)

                except Exception as e:
                    print(f"     ❌ Error analyzing list {list_info['name'][:30]}: {e}")
                    # Create error entry
                    error_list = OptoutList(
                        ia=list_info['ia'],
                        io=list_info['io'],
                        name=list_info['name'],
                        record_count=int(list_info.get('record_count', 0)),
                        created_date=datetime.now(),
                        description=f"Analysis failed: {e}",
                        list_type=list_info.get('type', 'Unknown'),
                        times_accessed=list_info.get('times_accessed', '0'),
                        last_accessed=list_info.get('last_accessed', 'Never'),
                        created_at='Unknown',
                        times_cleansed='Unknown',
                        downloaded_times='Unknown',
                        opt_out_list_id='Unknown',
                        status="error"
                    )
                    analyzed_lists.append(error_list)

            if analyzed_lists:
                # Save immediately to CSV
                saved_count = self._save_page_data(analyzed_lists, page_num)
                print(f"   ✅ Page {page_num} complete: {len(analyzed_lists)} analyzed, {saved_count} saved to disk")
                return saved_count
            else:
                print(f"   ⚠️  Page {page_num}: No lists were successfully analyzed")
                return 0

        except Exception as e:
            print(f"   ❌ Error processing page {page_num}: {e}")
            import traceback
            traceback.print_exc()
            return 0

    def run_page_by_page_analysis(self):
        """Run the complete page-by-page analysis."""
        print("🚀 PAGE-BY-PAGE OPTIZMO ANALYSIS - IMMEDIATE SAVE")
        print("=" * 70)

        start_time = datetime.now()
        print(f"⏰ Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 CSV file: {self.csv_filename}")

        if self.processed_lists:
            print(f"🔄 Resuming analysis - {len(self.processed_lists)} lists already processed")

        try:
            # Initialize CSV
            self._initialize_csv()

            # Create configuration and analyzer
            config = OptizmoConfig.from_env()
            print(f"✅ Configuration loaded (Headless: {config.headless_browser})")

            analyzer = OptizmoAnalyzer(config)
            print(f"✅ Analyzer created")

            print(f"\n🔍 STARTING PAGE-BY-PAGE PROCESSING")
            print("=" * 50)

            # Create web scraper for page-by-page processing
            web_scraper = OptizmoWebScraper(config)
            web_scraper._setup_driver()

            # Store web scraper reference for detail extraction
            self._web_scraper = web_scraper

            # Login first
            print(f"🔐 Logging into Optizmo...")
            web_scraper.login()
            print(f"✅ Login successful")

            # Process pages one by one
            page_num = 1
            page_offset = 0
            total_processed = 0
            total_saved = 0

            while True:
                print(f"\n📄 ===== DISCOVERING PAGE {page_num} (offset={page_offset}) =====")

                try:
                    # Navigate to this specific page
                    page_url = f"{config.web_base_url}/optouts.html?page={page_offset}"
                    print(f"🌐 Navigating to: {page_url}")
                    web_scraper.driver.get(page_url)

                    # Discover lists on this page only
                    page_lists = web_scraper.discover_optout_lists_improved()

                    if not page_lists:
                        print(f"   ⚠️  Page {page_num}: No lists found")
                        break

                    print(f"   ✅ Page {page_num}: Found {len(page_lists)} lists")

                    # Process this page immediately (discover + analyze + save)
                    saved_count = self._process_single_page(analyzer, page_lists, page_num)
                    total_saved += saved_count
                    total_processed += len(page_lists)

                    # Check for next page
                    has_next = web_scraper._check_for_next_page()

                    if not has_next:
                        print(f"\n🎉 Reached last page (page {page_num})")
                        break

                    # Move to next page
                    page_num += 1
                    page_offset += 70  # Based on your pagination pattern

                    # Show overall progress
                    print(f"📈 Overall progress: {page_num-1} pages processed, {total_saved} records saved")

                except Exception as e:
                    print(f"❌ Error processing page {page_num}: {e}")
                    import traceback
                    traceback.print_exc()

                    # Try to continue with next page
                    page_num += 1
                    page_offset += 70
                    continue

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n🎉 PAGE-BY-PAGE ANALYSIS COMPLETE!")
            print("=" * 50)
            print(f"⏰ Started:  {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏰ Finished: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  Duration: {duration}")
            print(f"📊 Total pages processed: {page_num-1}")
            print(f"📊 Total lists discovered: {total_processed}")
            print(f"📊 Total records saved: {total_saved}")
            print(f"📁 CSV file: {self.csv_filename}")
            print(f"📍 Location: {Path.cwd() / self.csv_filename}")

            # Show final CSV stats
            try:
                with open(self.csv_filename, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    final_count = sum(1 for row in reader)
                    print(f"📊 Final CSV contains: {final_count} total records")
            except:
                pass

            print(f"\n🚀 SUCCESS! Open '{self.csv_filename}' to view all results!")
            print(f"💡 TIP: Each page was saved immediately - no data loss possible!")

        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            print(f"\n💡 TIP: Run this script again to resume from where it left off!")

        finally:
            # Clean up
            try:
                if 'web_scraper' in locals() and hasattr(web_scraper, 'driver') and web_scraper.driver:
                    web_scraper.driver.quit()
                    print(f"🔧 Browser closed")
            except:
                pass


def main():
    """Main function to run page-by-page analysis."""
    # Check if user wants to resume from existing file
    existing_files = [f for f in os.listdir('.') if f.startswith('optizmo_page_by_page_') and f.endswith('.csv')]

    if existing_files:
        print(f"🔍 Found existing page-by-page analysis files:")
        for i, filename in enumerate(existing_files, 1):
            print(f"   {i}. {filename}")

        print(f"\nOptions:")
        print(f"   0. Start fresh analysis")
        print(f"   1-{len(existing_files)}. Resume from existing file")

        try:
            choice = input(f"\nEnter your choice (0-{len(existing_files)}): ").strip()

            if choice == "0":
                analyzer = PageByPageAnalyzer()
            elif choice.isdigit() and 1 <= int(choice) <= len(existing_files):
                selected_file = existing_files[int(choice) - 1]
                print(f"🔄 Resuming from: {selected_file}")
                analyzer = PageByPageAnalyzer(selected_file)
            else:
                print(f"❌ Invalid choice. Starting fresh analysis.")
                analyzer = PageByPageAnalyzer()
        except KeyboardInterrupt:
            print(f"\n👋 Cancelled by user")
            return
        except:
            print(f"❌ Invalid input. Starting fresh analysis.")
            analyzer = PageByPageAnalyzer()
    else:
        analyzer = PageByPageAnalyzer()

    analyzer.run_page_by_page_analysis()


if __name__ == "__main__":
    main()
