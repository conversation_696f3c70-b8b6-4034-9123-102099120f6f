#!/usr/bin/env python3
"""
Test script to verify detail extraction for a single list.
"""

import sys
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.web_scraper import OptizmoWebScraper


def test_single_detail_extraction():
    """Test detail extraction for a single known list."""
    print("🧪 Testing Single Detail Extraction")
    print("=" * 50)
    
    try:
        # Create configuration
        config = OptizmoConfig.from_env()
        print(f"✅ Configuration loaded")
        
        # Create web scraper
        with OptizmoWebScraper(config) as scraper:
            print(f"\n🔐 Logging in...")
            scraper.login()
            print(f"✅ Login successful")
            
            # Test detail extraction for a known list
            # Using the "Ace Hardware at Bizaglo" list from the previous output
            ia = "d6ac2d6c5d03bc9a"
            io = "4628f1744a3c6682"
            
            print(f"\n🔍 Testing detail extraction for:")
            print(f"   IA: {ia}")
            print(f"   IO: {io}")
            print(f"   Expected: Ace Hardware at Bizaglo list")
            
            details = scraper._get_list_details(ia, io)
            
            print(f"\n📊 Extraction Results:")
            print(f"   Created At: {details['created_at']}")
            print(f"   Times Cleansed: {details['times_cleansed']}")
            print(f"   Downloaded Times: {details['downloaded_times']}")
            print(f"   Opt-Out List ID: {details['opt_out_list_id']}")
            
            # Check if any fields were successfully extracted
            success_count = 0
            for field, value in details.items():
                if value != "Unknown":
                    success_count += 1
            
            print(f"\n📈 Summary:")
            print(f"   Fields extracted: {success_count}/4")
            if success_count > 0:
                print(f"   ✅ Detail extraction is working!")
            else:
                print(f"   ❌ Detail extraction failed - all fields remain 'Unknown'")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_single_detail_extraction()
