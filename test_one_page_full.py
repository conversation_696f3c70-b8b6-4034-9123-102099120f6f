#!/usr/bin/env python3
"""
Test script to verify the complete flow (Phase 1 + Phase 2) for just one page.
"""

import sys
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.web_scraper import OptizmoWebScraper


def test_one_page_full():
    """Test the complete flow for one page only."""
    print("🧪 Testing Complete Flow (Phase 1 + Phase 2) - One Page Only")
    print("=" * 70)

    try:
        # Create configuration
        config = OptizmoConfig.from_env()
        print(f"✅ Configuration loaded")

        # Create web scraper
        with OptizmoWebScraper(config) as scraper:
            print(f"\n🔐 Logging in...")
            scraper.login()
            print(f"✅ Login successful")

            print(f"\n📋 Phase 1: Discovering lists from first page only...")

            # Navigate to the lists page
            lists_url = f"{config.web_base_url}/optouts.html"
            print(f"🌐 Navigating to: {lists_url}")
            scraper.driver.get(lists_url)

            import time
            time.sleep(3)  # Wait for page to load

            # Discover lists from just the first page (no pagination)
            discovered_lists = scraper.discover_optout_lists_improved()

            if not discovered_lists:
                print(f"❌ No lists discovered on first page")
                return

            print(f"✅ Phase 1 Complete: Found {len(discovered_lists)} lists on first page")

            # Show sample of discovered lists
            print(f"\n📋 Sample of discovered lists (first 3):")
            for i, list_info in enumerate(discovered_lists[:3], 1):
                print(f"   {i}. {list_info['name'][:50]}...")
                print(f"      IA: {list_info['ia']}, IO: {list_info['io']}")
                print(f"      Type: {list_info['type']}, Records: {list_info['record_count']}")
                print(f"      Accessed: {list_info['times_accessed']}x, Last: {list_info['last_accessed']}")
                print()

            print(f"\n📋 Phase 2: Getting additional details for first 5 lists...")

            # Get details for first 5 lists only
            successful_extractions = 0
            failed_extractions = 0

            for i, list_info in enumerate(discovered_lists[:5], 1):
                print(f"\n   🔍 ({i}/5) Getting details for: {list_info['name'][:40]}...")

                try:
                    additional_details = scraper._get_list_details(list_info['ia'], list_info['io'])

                    # Update the list info with additional details
                    list_info['created_at'] = additional_details['created_at']
                    list_info['times_cleansed'] = additional_details['times_cleansed']
                    list_info['opt_out_list_id'] = additional_details['opt_out_list_id']
                    list_info['downloaded_times'] = additional_details['downloaded_times']

                    # Check if extraction was successful
                    extracted_fields = 0
                    for field in ['created_at', 'times_cleansed', 'downloaded_times', 'opt_out_list_id']:
                        if additional_details[field] != 'Unknown':
                            extracted_fields += 1

                    if extracted_fields > 0:
                        successful_extractions += 1
                        print(f"     ✅ Success: {extracted_fields}/4 fields extracted")
                        print(f"        Created: {additional_details['created_at']}")
                        print(f"        Cleansed: {additional_details['times_cleansed']}x")
                        print(f"        Downloaded: {additional_details['downloaded_times']}x")
                        print(f"        OptOut ID: {additional_details['opt_out_list_id'][:12]}...")
                    else:
                        failed_extractions += 1
                        print(f"     ❌ Failed: No fields extracted")

                except Exception as e:
                    failed_extractions += 1
                    print(f"     ❌ Error: {e}")

            print(f"\n📊 Phase 2 Results:")
            print(f"   Lists processed: 5")
            print(f"   Successful extractions: {successful_extractions}")
            print(f"   Failed extractions: {failed_extractions}")
            print(f"   Success rate: {(successful_extractions / 5) * 100:.1f}%")

            print(f"\n📋 Final Results - Enhanced List Data (first 3):")
            for i, list_info in enumerate(discovered_lists[:3], 1):
                print(f"\n   {i}. {list_info['name'][:50]}...")
                print(f"      IA: {list_info['ia']}, IO: {list_info['io']}")
                print(f"      Type: {list_info['type']}, Records: {list_info['record_count']}")
                print(f"      Created: {list_info.get('created_at', 'Unknown')}")
                print(f"      Cleansed: {list_info.get('times_cleansed', 'Unknown')}x")
                print(f"      Downloaded: {list_info.get('downloaded_times', 'Unknown')}x")
                print(f"      Accessed: {list_info['times_accessed']}x, Last: {list_info['last_accessed']}")
                print(f"      OptOut ID: {list_info.get('opt_out_list_id', 'Unknown')}")

            if successful_extractions >= 3:  # At least 60% success rate
                print(f"\n🎉 SUCCESS! Detail extraction is working well!")
                print(f"   Ready to integrate into main code.")
            else:
                print(f"\n⚠️  Mixed results. Detail extraction needs more work.")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_one_page_full()
