#!/usr/bin/env python3
"""
Incremental Optizmo Analysis - Process and save data page by page with recovery capability
"""

import sys
import csv
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.analyzer import OptizmoAnalyzer
from optizmo_analyzer.models import OptoutList


class IncrementalAnalyzer:
    """Analyzer that saves data incrementally and can resume from interruptions."""
    
    def __init__(self, csv_filename: str = None):
        self.csv_filename = csv_filename or f"optizmo_incremental_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.fieldnames = [
            'ia', 'io', 'list_name', 'list_type', 'items_count', 
            'times_accessed', 'last_accessed', 'created_at', 
            'times_cleansed', 'downloaded_times', 'opt_out_list_id'
        ]
        self.processed_lists = set()  # Track processed IA+IO combinations
        self.last_page_processed = -1
        
        # Load existing data if file exists
        self._load_existing_data()
    
    def _load_existing_data(self):
        """Load existing CSV data to determine where to resume."""
        if not os.path.exists(self.csv_filename):
            print(f"📄 Starting fresh analysis - CSV file will be: {self.csv_filename}")
            return
        
        print(f"📄 Found existing CSV file: {self.csv_filename}")
        
        try:
            with open(self.csv_filename, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                count = 0
                for row in reader:
                    ia_io = f"{row['ia']}_{row['io']}"
                    self.processed_lists.add(ia_io)
                    count += 1
                
                print(f"   📊 Found {count} existing records")
                print(f"   🔄 Will skip already processed lists and continue from where left off")
                
        except Exception as e:
            print(f"   ⚠️  Error reading existing CSV: {e}")
            print(f"   🔄 Will start fresh")
    
    def _initialize_csv(self):
        """Initialize CSV file with headers if it doesn't exist."""
        if not os.path.exists(self.csv_filename):
            with open(self.csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
                writer.writeheader()
                print(f"   ✅ CSV file initialized: {self.csv_filename}")
    
    def _save_page_data(self, optout_lists: List[OptoutList], page_num: int):
        """Save a page of analyzed data to CSV."""
        if not optout_lists:
            return
        
        new_records = 0
        skipped_records = 0
        
        with open(self.csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
            
            for optout_list in optout_lists:
                ia_io = f"{optout_list.ia}_{optout_list.io}"
                
                # Skip if already processed
                if ia_io in self.processed_lists:
                    skipped_records += 1
                    continue
                
                # Process field values
                list_type = getattr(optout_list, 'list_type', 'Unknown')
                if not list_type:
                    list_type = 'Unknown'
                
                times_accessed = getattr(optout_list, 'times_accessed', '0')
                if not times_accessed or times_accessed == 'Unknown':
                    times_accessed = '0'
                
                last_accessed = getattr(optout_list, 'last_accessed', 'Never')
                if not last_accessed or last_accessed == 'Unknown':
                    last_accessed = 'Never'
                
                created_at = getattr(optout_list, 'created_at', 'Never')
                if not created_at or created_at == 'Unknown':
                    created_at = 'Never'
                
                times_cleansed = getattr(optout_list, 'times_cleansed', '0')
                if not times_cleansed or times_cleansed == 'Unknown':
                    times_cleansed = '0'
                
                downloaded_times = getattr(optout_list, 'downloaded_times', '0')
                if not downloaded_times or downloaded_times == 'Unknown':
                    downloaded_times = '0'
                
                opt_out_list_id = getattr(optout_list, 'opt_out_list_id', 'Unknown')
                if not opt_out_list_id:
                    opt_out_list_id = 'Unknown'
                
                # Write row
                writer.writerow({
                    'ia': optout_list.ia,
                    'io': optout_list.io,
                    'list_name': optout_list.name,
                    'list_type': list_type,
                    'items_count': optout_list.record_count,
                    'times_accessed': times_accessed,
                    'last_accessed': last_accessed,
                    'created_at': created_at,
                    'times_cleansed': times_cleansed,
                    'downloaded_times': downloaded_times,
                    'opt_out_list_id': opt_out_list_id
                })
                
                # Track as processed
                self.processed_lists.add(ia_io)
                new_records += 1
        
        print(f"   💾 Page {page_num}: Saved {new_records} new records, skipped {skipped_records} duplicates")
        return new_records
    
    def run_incremental_analysis(self):
        """Run the complete incremental analysis."""
        print("🚀 INCREMENTAL OPTIZMO ANALYSIS - WITH AUTO-RECOVERY")
        print("=" * 70)
        
        start_time = datetime.now()
        print(f"⏰ Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 CSV file: {self.csv_filename}")
        
        if self.processed_lists:
            print(f"🔄 Resuming analysis - {len(self.processed_lists)} lists already processed")
        
        try:
            # Initialize CSV
            self._initialize_csv()
            
            # Create configuration and analyzer
            config = OptizmoConfig.from_env()
            print(f"✅ Configuration loaded (Headless: {config.headless_browser})")
            
            analyzer = OptizmoAnalyzer(config)
            print(f"✅ Analyzer created")
            
            print(f"\n🔍 PHASE 1: DISCOVERING ALL LISTS FROM ALL PAGES")
            print("=" * 50)
            
            # Discover ALL lists from ALL pages
            print(f"📋 Starting comprehensive list discovery...")
            discovered_lists = analyzer.discover_list_ids_from_web()
            
            if not discovered_lists:
                print(f"❌ No lists discovered")
                return
            
            total_lists = len(discovered_lists)
            print(f"\n✅ PHASE 1 COMPLETE!")
            print(f"   📊 Total lists discovered: {total_lists}")
            
            # Filter out already processed lists
            new_lists = []
            skipped_count = 0
            
            for list_info in discovered_lists:
                ia_io = f"{list_info['ia']}_{list_info['io']}"
                if ia_io not in self.processed_lists:
                    new_lists.append(list_info)
                else:
                    skipped_count += 1
            
            print(f"   🔄 Lists to process: {len(new_lists)} (skipping {skipped_count} already processed)")
            
            if not new_lists:
                print(f"🎉 All lists already processed! Analysis complete.")
                print(f"📁 Final CSV: {self.csv_filename}")
                return
            
            print(f"\n🔍 PHASE 2: ANALYZING LISTS IN BATCHES")
            print("=" * 50)
            
            # Process lists in batches (e.g., 10 at a time for frequent saves)
            batch_size = 10
            total_processed = 0
            total_saved = 0
            
            for i in range(0, len(new_lists), batch_size):
                batch = new_lists[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                total_batches = (len(new_lists) + batch_size - 1) // batch_size
                
                print(f"\n📊 Processing batch {batch_num}/{total_batches} ({len(batch)} lists)...")
                
                try:
                    # Analyze this batch
                    analysis = analyzer.analyze_multiple_lists(batch)
                    
                    if analysis and analysis.lists:
                        # Save this batch immediately
                        saved_count = self._save_page_data(analysis.lists, batch_num)
                        total_saved += saved_count
                        total_processed += len(analysis.lists)
                        
                        print(f"   ✅ Batch {batch_num} complete: {len(analysis.lists)} analyzed, {saved_count} saved")
                        
                        # Show progress
                        progress = (total_processed / len(new_lists)) * 100
                        print(f"   📈 Overall progress: {progress:.1f}% ({total_processed}/{len(new_lists)})")
                        
                    else:
                        print(f"   ⚠️  Batch {batch_num} returned no results")
                        
                except Exception as e:
                    print(f"   ❌ Error processing batch {batch_num}: {e}")
                    print(f"   🔄 Continuing with next batch...")
                    continue
            
            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n🎉 INCREMENTAL ANALYSIS COMPLETE!")
            print("=" * 50)
            print(f"⏰ Started:  {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏰ Finished: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  Duration: {duration}")
            print(f"📊 Total lists discovered: {total_lists}")
            print(f"📊 New lists processed: {total_processed}")
            print(f"📊 Records saved: {total_saved}")
            print(f"📁 CSV file: {self.csv_filename}")
            print(f"📍 Location: {Path.cwd() / self.csv_filename}")
            
            # Show final CSV stats
            try:
                with open(self.csv_filename, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    final_count = sum(1 for row in reader)
                    print(f"📊 Final CSV contains: {final_count} total records")
            except:
                pass
            
            print(f"\n🚀 SUCCESS! Open '{self.csv_filename}' to view all results!")
            print(f"💡 TIP: If interrupted, just run this script again to resume where it left off!")
                    
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            print(f"\n💡 TIP: Run this script again to resume from where it left off!")


def main():
    """Main function to run incremental analysis."""
    # Check if user wants to resume from existing file
    existing_files = [f for f in os.listdir('.') if f.startswith('optizmo_incremental_') and f.endswith('.csv')]
    
    if existing_files:
        print(f"🔍 Found existing incremental analysis files:")
        for i, filename in enumerate(existing_files, 1):
            print(f"   {i}. {filename}")
        
        print(f"\nOptions:")
        print(f"   0. Start fresh analysis")
        print(f"   1-{len(existing_files)}. Resume from existing file")
        
        try:
            choice = input(f"\nEnter your choice (0-{len(existing_files)}): ").strip()
            
            if choice == "0":
                analyzer = IncrementalAnalyzer()
            elif choice.isdigit() and 1 <= int(choice) <= len(existing_files):
                selected_file = existing_files[int(choice) - 1]
                print(f"🔄 Resuming from: {selected_file}")
                analyzer = IncrementalAnalyzer(selected_file)
            else:
                print(f"❌ Invalid choice. Starting fresh analysis.")
                analyzer = IncrementalAnalyzer()
        except KeyboardInterrupt:
            print(f"\n👋 Cancelled by user")
            return
        except:
            print(f"❌ Invalid input. Starting fresh analysis.")
            analyzer = IncrementalAnalyzer()
    else:
        analyzer = IncrementalAnalyzer()
    
    analyzer.run_incremental_analysis()


if __name__ == "__main__":
    main()
