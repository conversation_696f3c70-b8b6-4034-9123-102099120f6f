#!/usr/bin/env python3
"""
Final integration test to verify the complete flow works with the new downloaded_times field.
"""

import sys
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from optizmo_analyzer.config import OptizmoConfig
from optizmo_analyzer.analyzer import OptizmoAnalyzer


def test_final_integration():
    """Test the complete integration with the new downloaded_times field."""
    print("🧪 Final Integration Test - Complete Flow with Downloaded Times")
    print("=" * 80)
    
    try:
        # Create configuration
        config = OptizmoConfig.from_env()
        print(f"✅ Configuration loaded")
        
        # Create analyzer
        analyzer = OptizmoAnalyzer(config)
        print(f"✅ Analyzer created")
        
        print(f"\n📋 Phase 1: Discovering lists from web interface...")
        
        # Discover lists from web interface (limited to first page for testing)
        discovered_lists = analyzer.discover_list_ids_from_web()
        
        if not discovered_lists:
            print(f"❌ No lists discovered")
            return
        
        print(f"✅ Phase 1 Complete: Found {len(discovered_lists)} lists")
        
        # Show sample of discovered lists with new field
        print(f"\n📋 Sample of discovered lists with all fields (first 3):")
        for i, list_info in enumerate(discovered_lists[:3], 1):
            print(f"\n   {i}. {list_info['name'][:50]}...")
            print(f"      IA: {list_info['ia']}")
            print(f"      IO: {list_info['io']}")
            print(f"      Type: {list_info.get('type', 'Unknown')}")
            print(f"      Records: {list_info.get('record_count', 'Unknown')}")
            print(f"      Times Accessed: {list_info.get('times_accessed', 'Unknown')}")
            print(f"      Last Accessed: {list_info.get('last_accessed', 'Unknown')}")
            print(f"      Created At: {list_info.get('created_at', 'Unknown')}")
            print(f"      Times Cleansed: {list_info.get('times_cleansed', 'Unknown')}")
            print(f"      Downloaded Times: {list_info.get('downloaded_times', 'Unknown')}")  # NEW FIELD
            print(f"      Opt-Out List ID: {list_info.get('opt_out_list_id', 'Unknown')}")
        
        print(f"\n📋 Phase 2: Analyzing lists...")
        
        # Analyze the discovered lists (this will create OptoutList objects)
        analysis = analyzer.analyze_multiple_lists(discovered_lists[:5])  # Test with first 5 lists
        
        print(f"✅ Phase 2 Complete: Analyzed {len(analysis.lists)} lists")
        
        # Check if the new downloaded_times field is properly included
        print(f"\n📊 Verification of downloaded_times field in analysis:")
        successful_fields = 0
        total_fields = 0
        
        for i, optout_list in enumerate(analysis.lists[:3], 1):
            print(f"\n   {i}. {optout_list.name[:40]}...")
            print(f"      IA: {optout_list.ia}")
            print(f"      IO: {optout_list.io}")
            print(f"      Created At: {getattr(optout_list, 'created_at', 'NOT_SET')}")
            print(f"      Times Cleansed: {getattr(optout_list, 'times_cleansed', 'NOT_SET')}")
            print(f"      Downloaded Times: {getattr(optout_list, 'downloaded_times', 'NOT_SET')}")  # NEW FIELD
            print(f"      Opt-Out List ID: {getattr(optout_list, 'opt_out_list_id', 'NOT_SET')}")
            
            # Check if downloaded_times field exists and has a value
            downloaded_times = getattr(optout_list, 'downloaded_times', None)
            total_fields += 1
            if downloaded_times and downloaded_times != 'Unknown' and downloaded_times != 'NOT_SET':
                successful_fields += 1
                print(f"      ✅ Downloaded times field successfully populated: {downloaded_times}")
            else:
                print(f"      ❌ Downloaded times field not populated: {downloaded_times}")
        
        print(f"\n📈 Downloaded Times Field Summary:")
        print(f"   Lists checked: {total_fields}")
        print(f"   Lists with downloaded_times: {successful_fields}")
        print(f"   Success rate: {(successful_fields / total_fields) * 100:.1f}%")
        
        # Test CSV export with new field
        print(f"\n📄 Phase 3: Testing CSV export with downloaded_times field...")
        
        try:
            import csv
            import tempfile
            import os
            
            # Create a temporary CSV file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as temp_file:
                temp_csv_path = temp_file.name
            
            # Import the CSV save function
            from optizmo_analyzer.cli import _save_to_csv
            
            # Save to CSV
            _save_to_csv(analysis, temp_csv_path)
            
            # Read back and verify the CSV contains the downloaded_times field
            with open(temp_csv_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                fieldnames = reader.fieldnames
                
                print(f"   📋 CSV fieldnames: {fieldnames}")
                
                if 'downloaded_times' in fieldnames:
                    print(f"   ✅ downloaded_times field is present in CSV header")
                    
                    # Check first few rows
                    rows_with_data = 0
                    total_rows = 0
                    for row in reader:
                        total_rows += 1
                        if total_rows > 3:  # Check first 3 rows
                            break
                        
                        downloaded_times = row.get('downloaded_times', '')
                        print(f"   Row {total_rows}: downloaded_times = '{downloaded_times}'")
                        
                        if downloaded_times and downloaded_times != '0' and downloaded_times != 'Unknown':
                            rows_with_data += 1
                    
                    print(f"   📊 CSV rows with downloaded_times data: {rows_with_data}/{total_rows}")
                    
                else:
                    print(f"   ❌ downloaded_times field is missing from CSV header")
            
            # Clean up
            os.unlink(temp_csv_path)
            print(f"   ✅ CSV export test completed")
            
        except Exception as e:
            print(f"   ❌ CSV export test failed: {e}")
        
        # Final summary
        if successful_fields >= 2:  # At least 2 out of 3 lists should have the field
            print(f"\n🎉 SUCCESS! Integration test passed!")
            print(f"   ✅ Phase 1: List discovery working")
            print(f"   ✅ Phase 2: Detail extraction working with downloaded_times field")
            print(f"   ✅ Phase 3: CSV export includes downloaded_times field")
            print(f"   🚀 Ready for production use!")
        else:
            print(f"\n⚠️  Partial success. Some issues detected:")
            print(f"   ✅ Phase 1: List discovery working")
            print(f"   ❌ Phase 2: downloaded_times field extraction needs improvement")
            print(f"   ⚠️  Check the detail extraction logic")
                
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_final_integration()
